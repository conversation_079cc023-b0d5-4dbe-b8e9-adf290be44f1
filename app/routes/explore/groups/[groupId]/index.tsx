import type { Route } from "./+types/index";
import { Link, useParams } from "react-router";
import { useState, useMemo } from "react";
import {
  ArrowLeft,
  Star,
  Users,
  Globe,
  BookOpen,
  MessageSquare,
  Calendar,
  Share2,
  MoreHorizontal,
  Check,
} from "lucide-react";
import type { Module } from "~/lib/api/types";

import {
  useGroup,
  useGroupCohorts,
  useMyGroups,
  useCourse,
} from "~/lib/api/client-queries";

import type { Course } from "~/lib/api/types";
import { CourseTOC } from "~/components/CourseTOC";
import { CohortSelectionModal } from "~/components/CohortSelectionModal";

export function meta() {
  return [
    { title: "Group - Sphere" },
    { name: "description", content: "Group overview" },
  ];
}

export default function GroupPage() {
  const { groupId } = useParams();

  // Primary data queries
  const {
    data: groupData,
    isLoading: groupLoading,
    isError: groupError,
  } = useGroup(groupId!);

  const {
    data: cohortsData,
    isLoading: cohortsLoading,
    isError: cohortsError,
  } = useGroupCohorts(groupId!);

  const { data: myGroupsData } = useMyGroups();

  // Determine joined cohort IDs
  const joinedCohortIds: number[] = useMemo(() => {
    if (!myGroupsData) return [];
    const grp = myGroupsData.groups.byId[groupId ?? ""];
    return grp ? grp.joinedCohorts.map((c) => c.id) : [];
  }, [myGroupsData, groupId]);

  // Identify course module & fetch course details lazily
  const courseModule = groupData?.data?.defaultCohortModules?.find(
    (m: Module) => m.type === "course"
  );

  const courseId =
    courseModule &&
    courseModule.type === "course" &&
    courseModule.config &&
    "courseId" in courseModule.config
      ? String(courseModule.config.courseId)
      : null;

  const { data: courseResponse } = useCourse(courseId ?? "0");

  const courseData: Course | undefined = courseResponse?.data;

  const [showCohortModal, setShowCohortModal] = useState(false);

  // Loading / error handling
  if (groupLoading || cohortsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900 text-gray-400">
        Loading group…
      </div>
    );
  }

  if (groupError || cohortsError || !groupData || !cohortsData) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900 text-red-500">
        Failed to load group.
      </div>
    );
  }

  const group = groupData.data;
  const cohorts = cohortsData.data.cohorts;

  // Find the first open cohort for enrollment (based on dates)
  const now = new Date();
  const openCohort = cohorts.find((cohort) => {
    if (cohort.isDefault) return false;
    const startDate = new Date(cohort.startDate);
    const endDate = new Date(cohort.endDate);
    return startDate > now || (startDate <= now && endDate >= now);
  });

  const defaultCohort = cohorts.find((cohort) => cohort.isDefault);

  return (
    <div className="relative min-h-screen bg-black text-white">
      {/* Header */}
      <header className="sticky top-0 z-20 bg-black/80 backdrop-blur-md border-b border-gray-800">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            to="/explore"
            className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Back to Explore</span>
          </Link>

          <div className="flex items-center gap-4">
            <button className="p-2 hover:bg-gray-800 rounded-lg transition-colors">
              <Share2 className="w-5 h-5" />
            </button>
            <button className="p-2 hover:bg-gray-800 rounded-lg transition-colors">
              <MoreHorizontal className="w-5 h-5" />
            </button>
          </div>
        </div>
      </header>

      {/* Content & Sidebar */}
      <div className="relative mx-auto max-w-7xl px-4 lg:px-8 flex gap-8">
        {/* Main Content */}
        <main className="flex-1 max-w-4xl lg:max-w-5xl overflow-visible py-4">
          {/* Hero Section */}
          <div className="relative rounded-lg overflow-hidden pb-24">
            <img
              src={group.bannerImage}
              alt={group.name}
              className="w-full h-[300px] object-cover"
            />
            <div className="h-3/4 absolute bottom-0 left-0 w-full bg-gradient-to-b from-transparent via-black to-black" />
            <div className="absolute bottom-0 left-0 p-6">
              <h1 className="text-2xl md:text-3xl font-semibold mb-2">
                {group.name}
              </h1>
              <p className="text-sm text-zinc-400">{group.description}</p>
            </div>
          </div>

          <div className="p-6">
            {/* What You Will Get Section */}
            {group.bio && (
              <section className="mb-12">
                <h2 className="text-lg font-bold mb-6">What You Will Get</h2>
                <div
                  className="prose prose-sm md:prose-base prose-invert space-y-4 [&>p]:text-zinc-400 [&>p]:text-sm"
                  dangerouslySetInnerHTML={{ __html: group.bio }}
                />
              </section>
            )}

            {/* About Creator Section */}
            <section className="mb-12">
              <h2 className="text-lg font-bold mb-6">About Creator</h2>
              <div className="bg-zinc-900 rounded-lg p-6">
                <div className="flex items-center gap-4 mb-4">
                  {group.creatorAvatarURL ? (
                    <img
                      src={group.creatorAvatarURL}
                      alt={group.creatorName}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-gray-700 flex items-center justify-center">
                      <span className="text-lg font-semibold">
                        {group.creatorName[0]}
                      </span>
                    </div>
                  )}
                  <div>
                    <h3 className="font-semibold">{group.creatorName}</h3>
                    <p className="text-sm text-gray-400">
                      World Master Class · 3 Courses
                    </p>
                  </div>
                </div>
                <p className="text-zinc-200">{group.creatorBio}</p>
              </div>
            </section>

            {/* Course Content Section */}
            {courseModule && (
              <section className="mb-12">
                <h2 className="text-xl font-semibold mb-6">Course Content</h2>
                <div className="bg-gray-900/50 rounded-lg p-6">
                  {courseData ? (
                    <CourseTOC course={courseData} />
                  ) : (
                    <p className="text-gray-400">Loading course content...</p>
                  )}
                </div>
              </section>
            )}
          </div>
        </main>

        {/* Right Sidebar */}
        <aside className="hidden lg:block w-[22rem] flex-shrink-0">
          <div className="lg:sticky lg:top-22 max-h-[calc(100vh-96px)] overflow-y-auto space-y-6">
            <div className="p-6 bg-zinc-900 rounded-lg text-center">
              <h2 className="text-xl font-bold mb-4">{group.name}</h2>

              {openCohort ? (
                <>
                  <button
                    onClick={() => setShowCohortModal(true)}
                    className="cursor-pointer block w-full bg-indigo-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-indigo-700 transition-colors text-center mb-4"
                  >
                    Join Now
                  </button>
                  <p className="text-sm text-gray-400">
                    Upcoming Cohort Starting on
                    <br />
                    <span className="text-white">
                      {new Date(openCohort.startDate).toLocaleDateString(
                        "en-US",
                        { month: "long", day: "numeric", year: "numeric" }
                      )}
                    </span>
                  </p>
                </>
              ) : (
                <div className="bg-zinc-900 text-zinc-500 italic py-3 px-6 rounded-lg text-center">
                  No open cohorts available
                </div>
              )}
            </div>

            {/* Rating */}
            <div className="flex items-center gap-2">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < 4
                        ? "fill-yellow-500 text-yellow-500"
                        : "text-gray-600"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-400">(32 reviews)</span>
            </div>

            {/* Stats */}
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-gray-400">
                <Users className="w-5 h-5" />
                <span>{group.totalMembers} members enrolled</span>
              </div>
              <div className="flex items-center gap-3 text-gray-400">
                <Globe className="w-5 h-5" />
                <span>English</span>
              </div>
              <div className="flex items-center gap-3 text-gray-400">
                <Globe className="w-5 h-5" />
                <span>100% Online</span>
              </div>
              <div className="flex items-center gap-3 text-gray-400">
                <BookOpen className="w-5 h-5" />
                <span>{group.totalLessons} Lessons - Course</span>
              </div>
              <div className="flex items-center gap-3 text-gray-400">
                <MessageSquare className="w-5 h-5" />
                <span>Feed & Discussion</span>
              </div>
              <div className="flex items-center gap-3 text-gray-400">
                <Calendar className="w-5 h-5" />
                <span>Live Events</span>
              </div>
            </div>
          </div>
        </aside>
      </div>

      {/* Cohort Selection Modal */}
      <CohortSelectionModal
        isOpen={showCohortModal}
        onClose={() => setShowCohortModal(false)}
        cohorts={cohorts}
        groupId={group.externalId}
        joinedCohortIds={joinedCohortIds}
      />
    </div>
  );
}
