import type { Route } from "./+types/chat";

import "~/styles/chat-layout.css";
import "~/styles/custom-message.css";
import "stream-chat-react/dist/css/v2/index.css";
import {
  generateExpoCSSVariables,
  generateStreamCSSVariables,
  expoTheme,
} from "~/lib/themes/expo-theme";

import { RequireAuth } from "~/lib/auth/session";
import { useAppContext } from "~/lib/providers/app-context";
import {
  CustomChannelHeader,
  CustomChannelListHeader,
  CustomChannelPreview,
  CustomMessage,
  CustomMessageInput,
} from "~/components/chat";

import {
  Channel,
  MessageInput,
  MessageList,
  Thread,
  Chat,
  ChannelList,
  Window,
  MessageTimestamp,
  type MessageTimestampProps,
} from "stream-chat-react";

// Clean chat page - all custom components moved to ~/components/chat/

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Chat - Sphere" },
    { name: "description", content: "Real-time chat messaging" },
  ];
}

const CustomMessageTimestamp = (props: MessageTimestampProps) => (
  <MessageTimestamp {...props} calendar={false} format={"h:mm A"} /> // calendar is enabled by default
);

export default function ChatPage() {
  const { chatClient, userId } = useAppContext();

  console.log("userId", userId);
  console.log("chatClient", chatClient);

  const filters = { type: "messaging", members: { $in: [userId] } } as any;
  const sort = { last_message_at: -1 } as any;

  // Apply Expo theme CSS variables
  const expoCSSVars = generateExpoCSSVariables();
  const streamCSSVars = generateStreamCSSVariables();

  if (!chatClient) {
    return <div>Loading chat...</div>;
  }

  return (
    <RequireAuth>
      <div
        className="h-full min-h-screen"
        style={{
          backgroundColor: expoTheme.colors.primary,
          ...expoCSSVars,
          ...streamCSSVars,
        }}
      >
        <Chat
          client={chatClient}
          theme="str-chat__theme-dark"
          // Pass custom components to Chat context
        >
          <div className="flex h-screen">
            {/* Channel List */}
            <div className="channel-list-container">
              <CustomChannelListHeader />
              <ChannelList
                filters={filters}
                sort={sort}
                Preview={CustomChannelPreview}
              />
            </div>

            {/* Main Chat Area */}
            <div className="flex-1 flex">
              <Channel
                MessageTimestamp={CustomMessageTimestamp}
                Message={CustomMessage}
              >
                <Window>
                  <CustomChannelHeader />
                  <div className="flex-1 bg-black overflow-hidden">
                    <MessageList noGroupByUser={false} />
                  </div>
                  <CustomMessageInput />
                </Window>
                <Thread />
              </Channel>
            </div>
          </div>
        </Chat>
      </div>
    </RequireAuth>
  );
}
