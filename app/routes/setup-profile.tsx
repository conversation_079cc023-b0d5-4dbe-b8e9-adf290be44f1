import { useState } from "react";
import { useNavigate } from "react-router";
import { LogOut } from "lucide-react";
import Session from "supertokens-web-js/recipe/session";

import { useEditProfile } from "~/lib/api/client-queries";
import { useAppContext } from "~/lib/providers/app-context";
import { queryClient } from "~/lib/providers/query-client";

export default function SetupProfilePage() {
  const navigate = useNavigate();
  const { cleanup } = useAppContext();
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const { mutate: editProfile, isPending } = useEditProfile();

  const handleSave = () => {
    editProfile(
      {
        firstName,
        lastName,
        gender: null,
        interestedTopics: [],
        phoneNumber: "",
      },
      {
        onSuccess: () => {
          navigate("/", { replace: true });
        },
      }
    );
  };

  const handleSignOut = async () => {
    await Session.signOut();
    cleanup();
    queryClient.clear();
    navigate("/login", { replace: true });
  };

  return (
    <div className="min-h-screen bg-gray-900 relative">
      {/* Logout button in top-right corner */}
      <button
        onClick={handleSignOut}
        className="absolute top-6 right-6 flex items-center gap-2 px-4 py-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors"
      >
        <LogOut size={20} />
        <span>Logout</span>
      </button>

      <div className="min-h-screen flex items-center justify-center px-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-white mb-2">
              Setup Your Profile
            </h1>
            <p className="text-gray-400">Tell us a bit about yourself</p>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-white font-semibold mb-2">
                First Name
              </label>
              <input
                type="text"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                placeholder="Enter your first name"
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500"
              />
            </div>

            <div>
              <label className="block text-white font-semibold mb-2">
                Last Name
              </label>
              <input
                type="text"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                placeholder="Enter your last name"
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500"
              />
            </div>

            <button
              onClick={handleSave}
              disabled={isPending || !firstName.trim() || !lastName.trim()}
              className={`w-full mt-6 py-3 rounded-lg font-semibold text-white transition-all ${
                isPending || !firstName.trim() || !lastName.trim()
                  ? "bg-red-500/50 cursor-not-allowed"
                  : "bg-red-500 hover:bg-red-600 active:scale-[0.98]"
              }`}
            >
              {isPending ? "Saving..." : "Save Profile"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
