import type { Route } from "./+types/home";
import { useEffect } from "react";
import { useNavigate } from "react-router";

import { RequireAuth } from "~/lib/auth/session";
import { useMyGroups } from "~/lib/api/client-queries";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Home - Sphere" },
    { name: "description", content: "Welcome to Sphere!" },
  ];
}

export default function Home() {
  const navigate = useNavigate();
  const { data: myGroupsResponse, isLoading, isError } = useMyGroups();

  useEffect(() => {
    // Don't redirect while loading
    if (isLoading) return;

    // If there's an error or no groups data, redirect to explore
    if (isError || !myGroupsResponse?.success) {
      navigate("/explore", { replace: true });
      return;
    }

    // Check if user has any subscribed groups
    const groups = myGroupsResponse.groups;
    if (groups.order.length > 0) {
      // Redirect to the first group
      const firstGroupId = groups.order[0];
      navigate(`/groups/${firstGroupId}`, { replace: true });
    } else {
      // No groups subscribed, redirect to explore
      navigate("/explore", { replace: true });
    }
  }, [myGroupsResponse, isLoading, isError, navigate]);

  // Show loading state while we determine where to redirect
  if (isLoading) {
    return (
      <RequireAuth>
        <div className="flex items-center justify-center min-h-screen bg-black text-white">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500 mx-auto mb-4"></div>
            <p className="text-gray-400">Loading...</p>
          </div>
        </div>
      </RequireAuth>
    );
  }

  // This should not be reached as we redirect in useEffect, but keeping as fallback
  return (
    <RequireAuth>
      <div className="flex items-center justify-center min-h-screen bg-black text-white">
        <div className="text-center">
          <p className="text-gray-400">Redirecting...</p>
        </div>
      </div>
    </RequireAuth>
  );
}
