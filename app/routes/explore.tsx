import type { Route } from "./+types/explore";
import { <PERSON> } from "react-router";
import { useState, useMemo } from "react";

import { useGroups } from "~/lib/api/client-queries";
import type { Group } from "~/lib/api/types";
import { HttpError } from "~/lib/api/types";
import { LoadMore } from "~/components/LoadMore";

// Local hero images added to /public/assets
const heroImg1 = "/assets/hero-1.png";
const heroImg2 = "/assets/hero-2.png";
const heroImg3 = "/assets/hero-3.png";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Explore - Sphere" },
    { name: "description", content: "Discover communities and courses" },
  ];
}

export default function Explore() {
  const [searchQuery, setSearchQuery] = useState("");
  const {
    data,
    isLoading,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGroups();

  // Flatten all pages of groups from infinite query
  const allGroups =
    data?.pages?.flatMap((page) => (page.success ? page.data.groups : [])) ||
    [];

  // Filter groups based on search query
  const groups = useMemo(() => {
    if (!searchQuery.trim()) return allGroups;

    const query = searchQuery.toLowerCase();
    return allGroups.filter(
      (group) =>
        group.name.toLowerCase().includes(query) ||
        group.description?.toLowerCase().includes(query) ||
        group.creatorName.toLowerCase().includes(query) ||
        group.organizationName.toLowerCase().includes(query)
    );
  }, [allGroups, searchQuery]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900 text-gray-400">
        Loading groups…
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900 text-red-500">
        Failed to load groups.
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Main Content */}
      <main className="flex flex-col items-center px-4 py-12">
        {/* Hero Section */}
        <div className="-translate-y-10 text-center max-w-xl mb-12">
          {/* Hero Images */}
          <div className="grid grid-cols-3 gap-4 mb-8 max-w-lg mx-auto">
            <img
              src={heroImg1}
              alt="Community"
              className="h-full w-full object-cover rotate-[-17deg]"
            />
            <img
              src={heroImg2}
              alt="Learning"
              className="h-full w-full object-cover rotate-[-7deg]"
            />
            <img
              src={heroImg3}
              alt="Growth"
              className="h-full w-full object-cover rotate-[21deg] translate-y-4 translate-x-4"
            />
          </div>

          <p className="text-base mb-2 text-white font-bold font-merriweather">
            Find Your Tribe. Share Your Passion.
          </p>
          <h1 className="text-4xl md:text-6xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 to-white font-merriweather pb-4">
            Together We Rise
          </h1>
          <p className="text-sm md:text-lg text-gray-400 leading-snug">
            Transform yourself with leading communities, courses, memberships,
            and more
          </p>
        </div>

        {/* Search Bar */}
        <div className="w-full flex justify-center mb-12">
          <div className="relative w-full max-w-[600px]">
            <svg
              className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
            <input
              type="text"
              placeholder="Search groups..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-[#1a1a1a] text-gray-300 rounded-full py-3 pl-12 pr-4 border border-[#303030] focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>

        {/* Explore Section */}
        <div className="w-full max-w-6xl">
          <h2 className="text-lg font-semibold mb-6">Explore</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {groups.map((group) => (
              <GroupCard key={group.id} group={group} />
            ))}
          </div>

          {/* Load More Section - Only show when not searching */}
          {!searchQuery.trim() && (
            <LoadMore
              hasNextPage={hasNextPage}
              isFetchingNextPage={isFetchingNextPage}
              onLoadMore={() => fetchNextPage()}
            />
          )}

          {/* Search results info */}
          {searchQuery.trim() && (
            <div className="text-center mt-8 text-gray-400">
              {groups.length === 0 ? (
                <p>No groups found matching "{searchQuery}"</p>
              ) : (
                <p>
                  Found {groups.length} group{groups.length !== 1 ? "s" : ""}{" "}
                  matching "{searchQuery}"
                </p>
              )}
            </div>
          )}
        </div>
      </main>
    </div>
  );
}

function GroupCard({ group }: { group: Group }) {
  const isCohortOpen = group.tags?.includes("Cohort-Open");

  return (
    <Link to={`/explore/groups/${group.externalId}`} className="block">
      <div className="relative rounded-lg overflow-hidden hover:ring-2 hover:ring-indigo-500 transition-all cursor-pointer group">
        {/* Full card is the image background */}
        <div className="relative h-48">
          {group.bannerImage ? (
            <img
              src={group.bannerImage}
              alt={group.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gray-700 flex items-center justify-center">
              <svg
                className="w-16 h-16 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
          )}

          {/* Badge */}
          {isCohortOpen && (
            <div className="absolute top-3 right-3 bg-red-500 text-white text-[10px] font-semibold px-2 py-0.5 rounded-full uppercase">
              Cohort Open
            </div>
          )}

          {/* Dark gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-black/60 to-black" />

          {/* Content overlay at bottom */}
          <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
            <h3 className="font-semibold text-lg mb-1 leading-snug line-clamp-2">
              {group.name}
            </h3>
            <p className="text-xs text-zinc-500 font-medium">
              {group.creatorName}
            </p>
          </div>
        </div>
      </div>
    </Link>
  );
}

export function ErrorBoundary({ error }: Route.ErrorBoundaryProps) {
  return (
    <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold mb-4">Oops!</h1>
        <p className="text-gray-400 mb-4">
          {error instanceof HttpError
            ? error.message
            : "Something went wrong while loading groups."}
        </p>
        <a
          href="/explore"
          className="inline-block bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700"
        >
          Try Again
        </a>
      </div>
    </div>
  );
}
