import type { Route } from "./+types/google";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router";
import { signInAndUp } from "supertokens-web-js/recipe/thirdparty";
import { useSession } from "~/lib/auth/session";
import { useAppContext } from "~/lib/providers/app-context";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Google Login - Sphere" },
    { name: "description", content: "Processing Google login" },
  ];
}

export default function GoogleCallback() {
  const navigate = useNavigate();
  const { checkSession } = useSession();
  const { refreshAuth } = useAppContext();
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    handleGoogleCallback();
  }, []);

  async function handleGoogleCallback() {
    try {
      const response = await signInAndUp();

      if (response.status === "OK") {
        console.log("Google login successful:", response.user);

        // Update session state
        await checkSession();
        await refreshAuth();

        if (
          response.createdNewRecipeUser &&
          response.user.loginMethods.length === 1
        ) {
          // New user - might want to redirect to profile setup
          // For now, redirect to home
          navigate("/");
        } else {
          // Existing user - redirect to home
          navigate("/");
        }
      } else if (response.status === "SIGN_IN_UP_NOT_ALLOWED") {
        // The reason string is a user friendly message
        // about what went wrong. It can also contain a support code which users
        // can tell you so you know why their sign in / up was not allowed.
        setError(
          response.reason || "Sign in/up not allowed. Please contact support."
        );
        setIsProcessing(false);
      } else {
        // SuperTokens requires that the third party provider
        // gives an email for the user. If that's not the case, sign up / in
        // will fail.
        setError(
          "No email provided by Google. Please use another form of login."
        );
        setIsProcessing(false);

        // Redirect back to login page after a delay
        setTimeout(() => {
          navigate("/login");
        }, 3000);
      }
    } catch (err: any) {
      console.error("Google callback error:", err);

      if (err.isSuperTokensGeneralError === true) {
        // This may be a custom error message sent from the API by you.
        setError(err.message);
      } else {
        setError("Oops! Something went wrong during Google login.");
      }
      setIsProcessing(false);

      // Redirect back to login page after a delay
      setTimeout(() => {
        navigate("/login");
      }, 3000);
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center px-4 py-12">
      <div className="w-full max-w-md space-y-8 text-center">
        {isProcessing ? (
          <>
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-100 dark:bg-indigo-900/20">
              <svg
                className="animate-spin h-8 w-8 text-indigo-600 dark:text-indigo-400"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
            <h2 className="mt-6 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
              Completing Google Sign In
            </h2>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Please wait while we complete your authentication...
            </p>
          </>
        ) : (
          <>
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/20">
              <svg
                className="h-8 w-8 text-red-600 dark:text-red-400"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h2 className="mt-6 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
              Authentication Failed
            </h2>
            {error && (
              <div className="mt-4 rounded-md bg-red-50 p-4 dark:bg-red-900/20">
                <p className="text-sm text-red-800 dark:text-red-400">
                  {error}
                </p>
              </div>
            )}
            <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
              Redirecting you back to the login page...
            </p>
          </>
        )}
      </div>
    </div>
  );
}
