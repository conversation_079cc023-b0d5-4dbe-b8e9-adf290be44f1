import type { Route } from "./+types/signup";
import { Link, useNavigate } from "react-router";
import { useState } from "react";
import { signUp } from "supertokens-web-js/recipe/emailpassword";
import ThirdParty from "supertokens-web-js/recipe/thirdparty";

import { RequireNoAuth, useSession } from "~/lib/auth/session";

// Local hero images added to /public/assets
const heroImg1 = "/assets/hero-1.png";
const heroImg2 = "/assets/hero-2.png";
const heroImg3 = "/assets/hero-3.png";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Sign Up - Sphere" },
    { name: "description", content: "Create your Sphere account" },
  ];
}

export default function SignUp() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [authError, setAuthError] = useState("");

  const navigate = useNavigate();
  const { checkSession } = useSession();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setEmailError("");
    setPasswordError("");
    setAuthError("");

    if (password !== confirmPassword) {
      setPasswordError("Passwords do not match");
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await signUp({
        formFields: [
          {
            id: "email",
            value: email,
          },
          {
            id: "password",
            value: password,
          },
        ],
      });

      if (response.status === "OK") {
        // SuperTokens handles session creation automatically upon successful signup
        await checkSession();
        navigate("/"); // Redirect to home
      } else if (response.status === "FIELD_ERROR") {
        response.formFields.forEach((field) => {
          if (field.id === "email") {
            setEmailError(field.error);
          } else if (field.id === "password") {
            setPasswordError(field.error);
          }
        });
      } else if (response.status === "SIGN_UP_NOT_ALLOWED") {
        setAuthError(`Sign up not allowed. Support code: ${response.reason}`);
      } else {
        setAuthError("An unexpected sign up error occurred.");
      }
    } catch (err: any) {
      console.error(err);
      if (err.isSuperTokensGeneralError === true) {
        setAuthError(err.message);
      } else {
        setAuthError("Oops! Something went wrong.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleLogin = async () => {
    try {
      const authUrl =
        await ThirdParty.getAuthorisationURLWithQueryParamsAndSetState({
          thirdPartyId: "google",
          frontendRedirectURI: `${window.location.origin}/auth/callback/google`,
        });
      window.location.assign(authUrl);
    } catch (err: any) {
      console.error("Google signup error:", err);
      setAuthError("Failed to sign up with Google. Please try again.");
    }
  };

  return (
    <RequireNoAuth>
      <main className="min-h-screen bg-black text-white flex flex-col items-center px-4 py-12">
        {/* Hero images */}
        <div className="grid grid-cols-3 gap-4 mb-8 max-w-lg">
          <img
            src={heroImg1}
            alt="Community"
            className="h-full w-full object-cover rotate-[-17deg]"
          />
          <img
            src={heroImg2}
            alt="Learning"
            className="h-full w-full object-cover rotate-[-7deg]"
          />
          <img
            src={heroImg3}
            alt="Growth"
            className="h-full w-full object-cover rotate-[21deg] translate-y-4 translate-x-4"
          />
        </div>

        {/* Tagline & Heading */}
        <div className="-translate-y-10 text-center max-w-xl">
          <p className="text-base mb-2 text-white font-bold font-merriweather">
            Find Your Tribe. Share Your Passion.
          </p>
          <h1 className="text-4xl md:text-6xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-indigo-500 to-white font-merriweather pb-4">
            Together We Rise
          </h1>
          <p className="text-sm md:text-lg text-gray-400 leading-snug">
            Transform yourself with leading communities, courses, memberships,
            and more
          </p>
        </div>

        {/* Sign-up card */}
        <div className="w-full max-w-md bg-black border border-[#303030] rounded-md p-5 space-y-5">
          <div className="text-center space-y-1">
            <h2 className="text-lg font-semibold">Create Account</h2>
            <p className="text-sm text-gray-400">
              Or {""}
              <Link
                to="/login"
                className="font-medium text-indigo-400 hover:underline"
              >
                sign in to your existing account
              </Link>
            </p>
          </div>

          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            {authError && (
              <div className="rounded-md bg-red-50 p-4 dark:bg-red-900/20">
                <p className="text-sm text-red-800 dark:text-red-400">
                  {authError}
                </p>
              </div>
            )}
            <div className="space-y-4">
              <div>
                <label htmlFor="email" className="sr-only">
                  Email address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="block w-full rounded-md border border-[#303030] bg-black px-3 py-2 text-sm text-gray-200 placeholder-gray-500 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500"
                  placeholder="Email address"
                />
                {emailError && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {emailError}
                  </p>
                )}
              </div>
              <div>
                <label htmlFor="password" className="sr-only">
                  Password
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="block w-full rounded-md border border-[#303030] bg-black px-3 py-2 text-sm text-gray-200 placeholder-gray-500 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500"
                  placeholder="Password"
                />
                {passwordError && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {passwordError}
                  </p>
                )}
              </div>
              <div>
                <label htmlFor="confirm-password" className="sr-only">
                  Confirm Password
                </label>
                <input
                  id="confirm-password"
                  name="confirm-password"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="block w-full rounded-md border border-[#303030] bg-black px-3 py-2 text-sm text-gray-200 placeholder-gray-500 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500"
                  placeholder="Confirm password"
                />
              </div>
            </div>

            <div className="flex items-center">
              <input
                id="agree-terms"
                name="agree-terms"
                type="checkbox"
                required
                className="h-4 w-4 rounded border-[#303030] bg-black focus:ring-indigo-500"
              />
              <label
                htmlFor="agree-terms"
                className="ml-2 text-sm text-gray-300"
              >
                I agree to the {""}
                <a
                  href="#"
                  className="font-medium text-indigo-400 hover:underline"
                >
                  Terms and Conditions
                </a>
              </label>
            </div>

            <div>
              <button
                type="submit"
                disabled={isSubmitting}
                className="group relative flex w-full justify-center rounded-md border border-transparent bg-indigo-500 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? "Creating account..." : "Create account"}
              </button>
            </div>

            <hr className="border-[#303030]" />

            <div>
              <button
                type="button"
                onClick={handleGoogleLogin}
                className="group relative flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-black hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              >
                <svg
                  className="mr-2 h-5 w-5"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    fill="#4285F4"
                  />
                  <path
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    fill="#34A853"
                  />
                  <path
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    fill="#FBBC05"
                  />
                  <path
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    fill="#EA4335"
                  />
                </svg>
                Sign up with Google
              </button>
            </div>
          </form>
        </div>
      </main>
    </RequireNoAuth>
  );
}
