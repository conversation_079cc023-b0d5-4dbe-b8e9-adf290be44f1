import { useState } from "react";
import { useNavigate } from "react-router";
import { useProfile, useEditProfile } from "~/lib/api/client-queries";
import { Camera, Edit2, Save, X, LogOut } from "lucide-react";
import Session from "supertokens-web-js/recipe/session";
import { useAppContext } from "~/lib/providers/app-context";
import { queryClient } from "~/lib/providers/query-client";

export default function ProfilePage() {
  const navigate = useNavigate();
  const { cleanup } = useAppContext();
  const { data: profileData, isLoading } = useProfile();
  const editProfileMutation = useEditProfile();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    interestedTopics: [] as string[],
    phoneNumber: "",
    gender: null as string | null,
  });

  const profile = profileData?.data;

  const handleEdit = () => {
    if (profile) {
      setFormData({
        firstName: profile.firstName || "",
        lastName: profile.lastName || "",
        interestedTopics: profile.interestedTopics || [],
        phoneNumber: profile.phoneNumber || "",
        gender: profile.gender,
      });
      setIsEditing(true);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  const handleSave = async () => {
    try {
      await editProfileMutation.mutateAsync({
        firstName: formData.firstName,
        lastName: formData.lastName,
        interestedTopics: formData.interestedTopics,
        phoneNumber: formData.phoneNumber,
        gender: formData.gender,
      });
      setIsEditing(false);
    } catch (error) {
      // Update failed
    }
  };

  const handleSignOut = async () => {
    await Session.signOut();

    // IMPORTANT: cleanup all the context state here
    cleanup();
    queryClient.clear();

    navigate("/login", { replace: true });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-black">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-400"></div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="flex items-center justify-center h-screen bg-black">
        <p className="text-gray-400">Failed to load profile</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black">
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-zinc-900 rounded-lg border border-gray-800">
          {/* Header Section */}
          <div className="relative">
            <div className="h-32 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-t-lg"></div>
            <div className="absolute -bottom-16 left-8">
              <div className="relative">
                <div className="w-32 h-32 rounded-full border-4 border-zinc-900 bg-gray-800 overflow-hidden">
                  {profile.avatarUrl ? (
                    <img
                      src={profile.avatarUrl}
                      alt={`${profile.firstName} ${profile.lastName}`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-indigo-400 to-purple-600 flex items-center justify-center">
                      <span className="text-white text-4xl font-semibold">
                        {profile.firstName?.charAt(0).toUpperCase() ||
                          profile.email?.charAt(0).toUpperCase() ||
                          "U"}
                      </span>
                    </div>
                  )}
                </div>
                <button className="absolute bottom-0 right-0 bg-zinc-800 hover:bg-zinc-700 rounded-full p-2 border border-gray-700 transition-colors">
                  <Camera className="w-5 h-5 text-gray-300" />
                </button>
              </div>
            </div>
          </div>

          {/* Profile Content */}
          <div className="pt-20 px-8 pb-8 relative">
            <div className="absolute top-8 right-8">
            {!isEditing ? (
                <button
                  onClick={handleEdit}
                  className="flex items-center gap-2 px-4 py-2 bg-zinc-800 hover:bg-zinc-700 text-white rounded-md transition-colors border border-gray-700"
                >
                  <Edit2 className="w-4 h-4" />
                  Edit Profile
                </button>
              ) : (
                <div className="flex gap-2">
                  <button
                    onClick={handleSave}
                    disabled={editProfileMutation.isPending}
                    className="flex items-center gap-2 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors disabled:opacity-50"
                  >
                    <Save className="w-4 h-4" />
                    Save
                  </button>
                  <button
                    onClick={handleCancel}
                    className="flex items-center gap-2 px-4 py-2 bg-zinc-800 hover:bg-zinc-700 text-white rounded-md transition-colors border border-gray-700"
                  >
                    <X className="w-4 h-4" />
                    Cancel
                  </button>
                </div>
              )}
            </div>
            <div className="flex justify-between items-start mb-8">
              <div>
                {isEditing ? (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Name
                      </label>
                      <div className="flex gap-3">
                        <input
                          type="text"
                          value={formData.firstName}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              firstName: e.target.value,
                            })
                          }
                          className="flex-1 px-3 py-2 bg-zinc-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                          placeholder="First Name"
                        />
                        <input
                          type="text"
                          value={formData.lastName}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              lastName: e.target.value,
                            })
                          }
                          className="flex-1 px-3 py-2 bg-zinc-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                          placeholder="Last Name"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Email
                      </label>
                      <p className="text-gray-400 text-sm">{profile.email}</p>
                    </div>
                  </div>
                ) : (
                  <div>
                    <h1 className="text-3xl font-bold text-white">
                      {`${profile.firstName || ""} ${
                        profile.lastName || ""
                      }`.trim() || "Unnamed User"}
                    </h1>
                    <p className="text-gray-400 mt-1">{profile.email}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Profile Details */}
            <div className="space-y-6">
              {!isEditing && (
                <div>
                  <h3 className="text-sm font-medium text-gray-300 mb-2">
                    Phone Number
                  </h3>
                  <p className="text-white">
                    {profile.phoneNumber || "Not provided"}
                  </p>
                </div>
              )}

              {isEditing && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={(e) =>
                      setFormData({ ...formData, phoneNumber: e.target.value })
                    }
                    className="w-full px-3 py-2 bg-zinc-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    placeholder="Enter phone number"
                  />
                </div>
              )}

              {!isEditing && (
                <div>
                  <h3 className="text-sm font-medium text-gray-300 mb-2">
                    Role
                  </h3>
                  <p className="text-white">{profile.role || "User"}</p>
                </div>
              )}

              {!isEditing && (
                <div>
                  <h3 className="text-sm font-medium text-gray-300 mb-2">
                    Gender
                  </h3>
                  <p className="text-white">
                    {profile.gender || "Not specified"}
                  </p>
                </div>
              )}

              {isEditing && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Gender
                  </label>
                  <select
                    value={formData.gender || ""}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        gender: e.target.value || null,
                      })
                    }
                    className="w-full px-3 py-2 bg-zinc-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  >
                    <option value="">Not specified</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              )}

              {!isEditing && (
                <div>
                  <h3 className="text-sm font-medium text-gray-300 mb-2">
                    Interested Topics
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {profile.interestedTopics &&
                    profile.interestedTopics.length > 0 ? (
                      profile.interestedTopics.map((topic, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-indigo-500/20 text-indigo-300 rounded-full text-sm border border-indigo-500/30"
                        >
                          {topic}
                        </span>
                      ))
                    ) : (
                      <p className="text-gray-400">No topics selected</p>
                    )}
                  </div>
                </div>
              )}

              {isEditing && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Interested Topics
                  </label>
                  <textarea
                    value={formData.interestedTopics.join(", ")}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        interestedTopics: e.target.value
                          .split(",")
                          .map((t) => t.trim())
                          .filter(Boolean),
                      })
                    }
                    className="w-full px-3 py-2 bg-zinc-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none"
                    rows={3}
                    placeholder="Enter topics separated by commas"
                  />
                </div>
              )}

              {!isEditing && (
                <div>
                  <h3 className="text-sm font-medium text-gray-300 mb-2">
                    Member Since
                  </h3>
                  <p className="text-white">
                    {new Date(profile.createdAt).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </p>
                </div>
              )}
            </div>

            {/* Logout Button */}
            <div className="mt-8 pt-8 border-t border-gray-800">
              <button
                onClick={handleSignOut}
                className="flex items-center gap-2 px-4 py-2 text-gray-400 hover:text-white hover:bg-zinc-800 rounded-md transition-colors"
              >
                <LogOut size={20} />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
