import type { Route } from "./+types/index";
import { useNavigate } from "react-router";
import { useEffect } from "react";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Group - Sphere` },
    { name: "description", content: "Group overview" },
  ];
}

export default function GroupPage({ params }: Route.ComponentProps) {
  const navigate = useNavigate();
  const { groupId } = params;

  // Redirect to the first cohort if available
  useEffect(() => {
    // The group data is available from the parent layout
    // For now, we'll just show a welcome message
    // In a real app, you might redirect to the first cohort automatically
  }, [groupId, navigate]);

  return (
    <div className="flex items-center justify-center h-full bg-zinc-900">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-4">
          Welcome to the Group
        </h1>
        <p className="text-gray-400">
          Select a cohort from the sidebar to get started
        </p>
      </div>
    </div>
  );
}
