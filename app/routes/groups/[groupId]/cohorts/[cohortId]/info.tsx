import type { Route } from "./+types/info";
import { useParams } from "react-router";
import { InfoModule } from "~/components/modules/InfoModule";
import { useCohort } from "~/lib/api/client-queries";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Info - Sphere` },
    { name: "description", content: "Cohort information" },
  ];
}

export default function InfoPage() {
  const { groupId, cohortId } = useParams();

  const { data, isLoading, isError } = useCohort(groupId!, cohortId!);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900 text-gray-400">
        Loading cohort information…
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900 text-red-500">
        Failed to load cohort information.
      </div>
    );
  }

  const cohort = data?.data;

  return <InfoModule groupId={groupId!} cohort={cohort!} />;
}
