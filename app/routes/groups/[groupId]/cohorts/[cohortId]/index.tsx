import type { Route } from "./+types/index";
import { useParams } from "react-router";

import { useCohort } from "~/lib/api/client-queries";
import { HttpError, type Module, type Cohort } from "~/lib/api/types";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Cohort - Sphere` },
    { name: "description", content: "Cohort details and modules" },
  ];
}

export default function CohortPage() {
  const { groupId, cohortId } = useParams();

  const { data, isLoading, isError } = useCohort(groupId!, cohortId!);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900 text-gray-400">
        Loading cohort…
      </div>
    );
  }

  if (isError || !data) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900 text-red-500">
        Failed to load cohort.
      </div>
    );
  }

  const cohort = data.data;

  return (
    <div className="bg-gray-900 min-h-full">
      {/* Header with banner */}
      <div className="relative h-64 bg-gray-800">
        <div className="w-full h-full bg-gradient-to-br from-purple-600 to-indigo-600" />

        {/* Overlay with title */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent flex items-end">
          <div className="p-8 text-white">
            <h1 className="text-4xl font-bold mb-2">{cohort.name}</h1>
            {cohort.bio && (
              <p className="text-gray-200 max-w-2xl">{cohort.bio}</p>
            )}
          </div>
        </div>
      </div>

      {/* Modules Grid */}
      <div className="p-8">
        <h2 className="text-2xl font-semibold text-white mb-6">Modules</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {cohort.modules.map((module) => (
            <ModuleCard key={module.id} module={module} cohort={cohort} />
          ))}
          {/* Info Module - Always shown at the end */}
          <a
            href={`/groups/${groupId}/cohorts/${cohort.externalId}/info`}
            className="block bg-gray-800 rounded-lg p-6 hover:bg-gray-700 transition-colors"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="text-indigo-400">
                <svg
                  className="w-8 h-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <span className="text-xs text-gray-500 uppercase">info</span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Info</h3>
            <p className="text-sm text-gray-400">About this cohort</p>
          </a>
        </div>
      </div>
    </div>
  );
}

function ModuleCard({ module, cohort }: { module: Module; cohort: Cohort }) {
  const getModuleIcon = (type: string) => {
    switch (type) {
      case "course":
        return (
          <svg
            className="w-8 h-8"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
            />
          </svg>
        );
      case "feed":
        return (
          <svg
            className="w-8 h-8"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
            />
          </svg>
        );
      case "liveClass":
        return (
          <svg
            className="w-8 h-8"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
            />
          </svg>
        );
      default:
        return (
          <svg
            className="w-8 h-8"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
        );
    }
  };

  return (
    <a
      href={`/groups/${cohort.groupId}/cohorts/${cohort.externalId}/modules/${module.id}`}
      className="block bg-gray-800 rounded-lg p-6 hover:bg-gray-700 transition-colors"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="text-indigo-400">{getModuleIcon(module.type)}</div>
        <span className="text-xs text-gray-500 uppercase">{module.type}</span>
      </div>
      <h3 className="text-lg font-semibold text-white mb-2">{module.name}</h3>
      <p className="text-sm text-gray-400">Module {module.order}</p>
    </a>
  );
}
