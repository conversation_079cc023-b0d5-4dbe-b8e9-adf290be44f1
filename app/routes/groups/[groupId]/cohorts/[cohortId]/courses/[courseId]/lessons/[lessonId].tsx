import { <PERSON>, use<PERSON><PERSON><PERSON>, useLocation } from "react-router";
import type { Route } from "./+types/[lessonId]";
import { useCompleteLesson } from "~/lib/api/client-queries";
import { useCourse, useCohort, useLesson } from "~/lib/api/client-queries";
import { useState, useEffect } from "react";
import {
  ArrowLeft,
  Check,
  X,
  FileText,
  Link2,
  Play,
  Clock,
  ChevronRight,
  MonitorPlay,
  Volume2,
  File,
  CircleCheck,
  Layers,
} from "lucide-react";
import type {
  Lesson,
  Course,
  Section,
  LessonContent,
  Cohort,
  Module,
} from "~/lib/api/types";
import { VideoPlayerHLS } from "~/components/VideoPlayerHLS";

// Helper function to get lesson content type
function getLessonContentType(
  lesson: Lesson
): "video" | "audio" | "article" | "mixed" {
  if (!lesson.lessonContents || lesson.lessonContents.length === 0) {
    return "article";
  }

  const contentTypes = lesson.lessonContents.map(
    (content) => content.contentType
  );

  // If multiple different types, return "mixed"
  const uniqueTypes = [...new Set(contentTypes)];
  if (uniqueTypes.length > 1) return "mixed";

  // If only one type, return that type
  if (contentTypes.includes("video")) return "video";
  if (contentTypes.includes("audio")) return "audio";
  if (contentTypes.includes("article")) return "article";

  return "article";
}

// Helper function to get lesson icon based on content type
function getLessonIcon(contentType: "video" | "audio" | "article" | "mixed") {
  switch (contentType) {
    case "video":
      return <MonitorPlay className="w-5 h-5" />;
    case "audio":
      return <Volume2 className="w-5 h-5" />;
    case "mixed":
      return <Layers className="w-5 h-5" />;
    case "article":
    default:
      return <File className="w-5 h-5" />;
  }
}

// Helper function to format duration
function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${String(minutes).padStart(2, "0")}:${String(
    remainingSeconds
  ).padStart(2, "0")}`;
}

// Helper function to check if video URL is HLS format
const isHLSVideo = (url: string) => {
  return (
    url.includes(".m3u8") ||
    url.includes("/manifest/video.m3u8") ||
    url.includes("cloudflarestream.com") ||
    url.includes("videodelivery.net")
  );
};

export default function LessonPage() {
  const params = useParams();
  const location = useLocation();
  const { groupId, cohortId, courseId, lessonId } = params;
  const moduleIdFromState = (location.state as any)?.moduleId as
    | string
    | undefined;

  // Fetch course and cohort in parallel
  const {
    data: courseResponse,
    isLoading: courseLoading,
    isError: courseError,
  } = useCourse(courseId!);

  const {
    data: cohortResponse,
    isLoading: cohortLoading,
    isError: cohortError,
  } = useCohort(groupId!, cohortId!);

  // Determine section once course is loaded
  if (courseLoading || cohortLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-zinc-900 text-zinc-400">
        Loading lesson…
      </div>
    );
  }

  if (courseError || cohortError || !courseResponse) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-zinc-900 text-red-500">
        Failed to load lesson.
      </div>
    );
  }

  const course = courseResponse.data as Course;
  const cohort = cohortResponse?.data;

  const foundSection = course.sections.find((s: Section) =>
    s.lessons.some((l) => l.id === Number(lessonId))
  );

  if (!foundSection) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900 text-red-500">
        Lesson not found in course.
      </div>
    );
  }

  const sectionId = String(foundSection.id);

  return (
    <LessonContent
      course={course}
      cohort={cohort}
      sectionId={sectionId}
      params={params as Record<string, string>}
      moduleIdFromState={moduleIdFromState}
    />
  );
}

// ---------------------------- LessonContent ---------------------------

function LessonContent({
  course,
  cohort,
  sectionId,
  params,
  moduleIdFromState,
}: {
  course: Course;
  cohort?: Cohort;
  sectionId: string;
  params: Record<string, string>;
  moduleIdFromState?: string;
}) {
  const { lessonId, courseId } = params;

  /**
   * -----------------------------
   * Hooks – must run on EVERY render
   * -----------------------------
   */
  const [showSidebar, setShowSidebar] = useState(true);
  const { mutate: completeLesson, isPending } = useCompleteLesson();

  const {
    data: lessonResponse,
    isLoading: lessonLoading,
    isError: lessonError,
  } = useLesson(courseId!, sectionId, lessonId!);

  // Derive `lesson` once the query is resolved
  const lesson: Lesson | undefined = lessonResponse?.data;

  const currentSection = course.sections.find((section) =>
    lesson ? section.lessons.some((l) => l.id === lesson.id) : false
  );

  /**
   * -----------------------------
   * Conditional early returns AFTER hooks
   * -----------------------------
   */
  if (lessonLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-zinc-900 text-zinc-400">
        Loading lesson…
      </div>
    );
  }

  if (lessonError || !lesson) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-zinc-900 text-red-500">
        Failed to load lesson.
      </div>
    );
  }

  /**
   * -----------------------------
   * Render once data is ready
   * -----------------------------
   */

  const handleCompleteLesson = () => {
    if (lesson.isCompleted || !currentSection) return;

    completeLesson({
      courseId: courseId!,
      sectionId: String(currentSection.id),
      lessonId: lessonId!,
    });
  };

  // Calculate course progress
  const totalLessons = course.sections.reduce(
    (acc, section) => acc + section.lessons.length,
    0
  );
  const completedLessons = course.sections.reduce(
    (acc, section) => acc + section.lessons.filter((l) => l.isCompleted).length,
    0
  );
  const progressPercentage =
    totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;

  const matchedModule = moduleIdFromState
    ? { id: moduleIdFromState }
    : cohort?.modules.find(
        (m: any) =>
          m.type === "course" &&
          "courseId" in (m.config || {}) &&
          (String(m.config.courseId) === String(params.courseId) ||
            String(m.config.courseId) === String(course.id) ||
            String(m.config.courseId) === String(course.externalId))
      );

  const backToPath = matchedModule
    ? `/groups/${params.groupId}/cohorts/${params.cohortId}/modules/${matchedModule.id}`
    : `/groups/${params.groupId}/cohorts/${params.cohortId}`; // Fallback to cohort page if no module found

  return (
    <div className="flex h-screen bg-zinc-950">
      {/* Main Content */}
      <div className={`flex-1 flex flex-col ${showSidebar ? "mr-96" : ""}`}>
        {/* Header */}
        <div className="sticky top-0 z-20 flex items-center justify-between px-8 py-6 bg-black/30 backdrop-blur">
          <Link
            to={backToPath}
            className="flex items-center gap-3 text-zinc-300 hover:text-white transition-colors text-sm font-medium"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Course</span>
          </Link>

          <button
            onClick={handleCompleteLesson}
            disabled={lesson.isCompleted || isPending}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all text-sm ${
              lesson.isCompleted
                ? "bg-green-500/10 text-green-400 cursor-not-allowed border border-green-500/20"
                : "bg-indigo-600 text-white hover:bg-indigo-700"
            }`}
          >
            <Check className="w-4 h-4" />
            <span>{lesson.isCompleted ? "Completed" : "Mark as Complete"}</span>
          </button>
        </div>

        {/* Lesson Content */}
        <div className="">
          <div className="max-w-4xl mx-auto px-8 py-6">
            {/* 1. Video Content (if exists) */}
            {lesson.lessonContents
              .filter((content) => content.contentType === "video")
              .map((content, index) => (
                <div key={`video-${index}`} className="mb-8">
                  <div className="border border-zinc-800 relative aspect-video bg-zinc-900 rounded-xl overflow-hidden shadow-2xl">
                    {content.metadata.url.includes("processing") ? (
                      <div className="absolute inset-0 flex flex-col items-center justify-center">
                        <Clock className="w-12 h-12 text-indigo-500 mb-4" />
                        <h3 className="text-xl font-semibold text-white mb-2">
                          Video Processing
                        </h3>
                        <p className="text-zinc-400">
                          This video will be available soon
                        </p>
                      </div>
                    ) : isHLSVideo(content.metadata.url) ? (
                      <VideoPlayerHLS
                        videoUrl={content.metadata.url}
                        className="w-full h-full object-contain"
                      />
                    ) : (
                      <video
                        controls
                        className="w-full h-full object-contain"
                        src={content.metadata.url}
                      >
                        Your browser does not support the video tag.
                      </video>
                    )}
                  </div>
                </div>
              ))}

            {/* Placeholder if no content at all */}
            {lesson.lessonContents.length === 0 && (
              <div className="mb-8 aspect-video bg-zinc-900 rounded-xl overflow-hidden shadow-2xl">
                <img
                  src="https://images.unsplash.com/photo-1563986768494-4dee2763ff3f?w=800&q=80"
                  alt={lesson.title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}

            {/* 2. Title and Section Info */}
            <div className="mb-8">
              {currentSection && (
                <p className="text-zinc-500 text-sm font-bold">
                  {currentSection.name}
                </p>
              )}
              <h1 className="text-2xl font-bold text-white mb-3">
                {lesson.title}
              </h1>

              {lesson.description && (
                <p className="text-zinc-400 leading-relaxed text-sm">
                  {lesson.description}
                </p>
              )}
            </div>

            {/* 4. Audio Content (if exists) */}
            {lesson.lessonContents
              .filter((content) => content.contentType === "audio")
              .map((content, index) => (
                <div key={`audio-${index}`} className="mb-8">
                  {lesson.lessonContents.filter(
                    (c) => c.contentType === "audio"
                  ).length > 1 && (
                    <div className="mb-4">
                      <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                        <Volume2 className="w-5 h-5 text-purple-400" />
                        Audio Content {index + 1}
                      </h3>
                    </div>
                  )}
                  <div className="bg-zinc-900 rounded-xl p-6 shadow-lg">
                    <audio
                      controls
                      className="w-full"
                      src={content.metadata.url}
                    >
                      Your browser does not support the audio tag.
                    </audio>
                  </div>
                </div>
              ))}

            {/* 5. Article Content (if exists) */}
            {lesson.lessonContents
              .filter((content) => content.contentType === "article")
              .map((content, index) => (
                <div key={`article-${index}`} className="mb-8">
                  {lesson.lessonContents.filter(
                    (c) => c.contentType === "article"
                  ).length > 1 && (
                    <div className="mb-4">
                      <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                        <File className="w-5 h-5 text-green-400" />
                        Article Content {index + 1}
                      </h3>
                    </div>
                  )}
                  <div className="bg-zinc-900/50 rounded-xl p-6 border border-zinc-800">
                    <div className="prose prose-invert max-w-none">
                      <p className="text-zinc-300 leading-relaxed whitespace-pre-wrap text-sm">
                        {content.metadata.content}
                      </p>
                    </div>
                  </div>
                </div>
              ))}

            {/* 6. Attachments */}
            {lesson.lessonAttachments.length > 0 && (
              <div className="mb-8">
                <h2 className="text-base font-semibold text-white mb-4">
                  Attachments
                </h2>

                {/* Attachment List */}
                <div className="space-y-3">
                  {lesson.lessonAttachments.map((attachment) => (
                    <a
                      key={attachment.id}
                      href={attachment.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-4 p-4 bg-zinc-900/50 rounded-xl hover:bg-zinc-800/50 transition-colors border border-zinc-800 hover:border-zinc-700"
                    >
                      <div
                        className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                          attachment.attachmentType === "document"
                            ? "bg-zinc-700"
                            : "bg-indigo-600/20"
                        }`}
                      >
                        {attachment.attachmentType === "document" ? (
                          <FileText className="w-4 h-4 text-white" />
                        ) : (
                          <Link2 className="w-4 h-4 text-indigo-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h4 className="text-white font-medium text-sm">
                          {attachment.title}
                        </h4>
                        <p className="text-zinc-500 text-xs truncate">
                          {attachment.url}
                        </p>
                      </div>
                    </a>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Course Content Sidebar */}
      {showSidebar && (
        <div className="fixed right-0 top-0 h-full w-96 bg-zinc-900 border-l border-zinc-800/50 flex flex-col">
          {/* Sidebar Header */}
          <div className="flex items-center justify-between px-6 py-4 border-b border-zinc-800/50">
            <h2 className="text-base font-semibold text-white">
              Course Content
            </h2>
            <button
              onClick={() => setShowSidebar(false)}
              className="text-zinc-400 hover:text-white transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          {/* Progress Bar */}
          <div className="px-6 py-4 border-b border-zinc-800/50">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs text-zinc-400 uppercase tracking-wide">
                Progress
              </span>
              <span className="text-xs text-white font-medium">
                {completedLessons}/{totalLessons} lessons
              </span>
            </div>
            <div className="w-full bg-zinc-800 rounded-full h-1.5">
              <div
                className="bg-indigo-500 h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          </div>

          {/* Sections and Lessons */}
          <div className="flex-1 overflow-y-auto">
            <div className="space-y-4 p-4">
              {course.sections
                .sort((a, b) => a.sectionOrder - b.sectionOrder)
                .map((section) => {
                  const sectionCompleted =
                    section.lessons.length > 0 &&
                    section.lessons.every((l) => l.isCompleted);

                  return (
                    <div
                      key={section.id}
                      className="rounded-lg overflow-hidden border border-zinc-800"
                    >
                      {/* Section Header */}
                      <div className="px-4 py-3 bg-zinc-800/20">
                        <div className="flex items-center gap-3">
                          <h3 className="text-white font-semibold text-sm">
                            {section.name}
                          </h3>
                          {sectionCompleted && (
                            <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                              <Check className="w-2.5 h-2.5 text-white" />
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Section Lessons */}
                      <div className="divide-y divide-zinc-800">
                        {section.lessons
                          .sort((a, b) => a.lessonOrder - b.lessonOrder)
                          .map((sectionLesson) => {
                            const isCurrentLesson =
                              sectionLesson.id === lesson.id;

                            return (
                              <Link
                                key={sectionLesson.id}
                                to={`/groups/${params.groupId}/cohorts/${params.cohortId}/courses/${params.courseId}/lessons/${sectionLesson.id}`}
                                state={{
                                  moduleId:
                                    matchedModule?.id ?? moduleIdFromState,
                                }}
                                className={`flex items-center justify-between px-4 py-3 hover:bg-zinc-800/30 transition-colors ${
                                  isCurrentLesson
                                    ? "bg-zinc-800/50 border-l-2 border-l-indigo-500"
                                    : ""
                                }`}
                              >
                                <div className="flex items-center gap-3 grow">
                                  <div className="text-zinc-400">
                                    {getLessonIcon(
                                      getLessonContentType(sectionLesson)
                                    )}
                                  </div>
                                  <h4
                                    className={`truncate text-sm ${
                                      isCurrentLesson
                                        ? "text-white font-medium"
                                        : "text-zinc-300"
                                    }`}
                                  >
                                    {sectionLesson.title}
                                  </h4>
                                </div>
                                <span className="text-zinc-400 text-sm">
                                  {sectionLesson.isCompleted ? (
                                    <CircleCheck className="w-5 h-5 text-green-500" />
                                  ) : (
                                    <div className="text-zinc-400 text-xs font-mono">
                                      {formatDuration(90)}
                                    </div>
                                  )}
                                </span>
                              </Link>
                            );
                          })}
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>

          {/* Sidebar Footer */}
          <div className="p-6 border-t border-zinc-800/50">
            <button
              onClick={() => setShowSidebar(false)}
              className="w-full px-4 py-3 bg-zinc-800 text-zinc-300 rounded-lg hover:bg-zinc-700 transition-colors flex items-center justify-center gap-2 text-sm font-medium"
            >
              <span>Course Content</span>
            </button>
          </div>
        </div>
      )}

      {/* Toggle Sidebar Button (when hidden) */}
      {!showSidebar && (
        <button
          onClick={() => setShowSidebar(true)}
          className="fixed bottom-8 right-8 px-4 py-3 bg-zinc-800 text-zinc-300 rounded-xl hover:bg-zinc-700 transition-colors flex items-center gap-2 shadow-2xl text-sm font-medium"
        >
          <span>Course Content</span>
        </button>
      )}
    </div>
  );
}
