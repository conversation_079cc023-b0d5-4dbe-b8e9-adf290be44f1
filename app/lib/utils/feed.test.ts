/**
 * Tests for feed utility functions
 * Run with: npx tsx app/lib/utils/feed.test.ts
 */

import { extractOrgIdFromFeedId, canUserPostToFeed } from "./feed";

// Test extractOrgIdFromFeedId function
function testExtractOrgIdFromFeedId() {
  console.log("Testing extractOrgIdFromFeedId...");

  // Test valid feedId
  const validFeedId = "org-7_group-7_cohort-60_module-167";
  const result1 = extractOrgIdFromFeedId(validFeedId);
  console.assert(result1 === 7, `Expected 7, got ${result1}`);

  // Test another valid feedId
  const validFeedId2 = "org-123_group-456_cohort-789_module-101";
  const result2 = extractOrgIdFromFeedId(validFeedId2);
  console.assert(result2 === 123, `Expected 123, got ${result2}`);

  // Test invalid feedId
  const invalidFeedId = "invalid-format";
  const result3 = extractOrgIdFromFeedId(invalidFeedId);
  console.assert(result3 === null, `Expected null, got ${result3}`);

  // Test empty string
  const emptyFeedId = "";
  const result4 = extractOrgIdFromFeedId(emptyFeedId);
  console.assert(result4 === null, `Expected null, got ${result4}`);

  console.log("✅ extractOrgIdFromFeedId tests passed");
}

// Test canUserPostToFeed function
function testCanUserPostToFeed() {
  console.log("Testing canUserPostToFeed...");

  const feedId = "org-7_group-7_cohort-60_module-167";

  // Test creator with matching org
  const result1 = canUserPostToFeed("creator", 7, feedId);
  console.assert(result1 === true, `Expected true, got ${result1}`);

  // Test moderator with matching org
  const result2 = canUserPostToFeed("moderator", 7, feedId);
  console.assert(result2 === true, `Expected true, got ${result2}`);

  // Test creator with different org
  const result3 = canUserPostToFeed("creator", 8, feedId);
  console.assert(result3 === false, `Expected false, got ${result3}`);

  // Test moderator with different org
  const result4 = canUserPostToFeed("moderator", 8, feedId);
  console.assert(result4 === false, `Expected false, got ${result4}`);

  // Test member with matching org (should be false)
  const result5 = canUserPostToFeed("member", 7, feedId);
  console.assert(result5 === false, `Expected false, got ${result5}`);

  // Test undefined role
  const result6 = canUserPostToFeed(undefined, 7, feedId);
  console.assert(result6 === false, `Expected false, got ${result6}`);

  // Test undefined orgId
  const result7 = canUserPostToFeed("creator", undefined, feedId);
  console.assert(result7 === false, `Expected false, got ${result7}`);

  console.log("✅ canUserPostToFeed tests passed");
}

// Run tests
function runTests() {
  console.log("Running feed utility tests...\n");

  try {
    testExtractOrgIdFromFeedId();
    testCanUserPostToFeed();

    console.log("\n🎉 All tests passed!");
  } catch (error) {
    console.error("\n❌ Test failed:", error);
    process.exit(1);
  }
}

// Run tests
runTests();

export { runTests };
