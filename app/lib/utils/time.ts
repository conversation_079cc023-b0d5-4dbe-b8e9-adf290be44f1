import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

// Initialize dayjs plugins
dayjs.extend(relativeTime);
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Formats a UTC timestamp string to a human-readable relative time
 * @param dateString - UTC timestamp string (e.g., "2025-07-21T13:27:25.002746")
 * @returns Formatted time string (e.g., "2h", "3d", "Just now")
 */
export function formatTimeAgo(dateString: string): string {
  const date = dayjs.utc(dateString).local();
  const now = dayjs();
  const diffInSeconds = now.diff(date, "second");

  if (diffInSeconds < 60) return "Just now";
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m`;
  }
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h`;
  }
  if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d`;
  }

  return date.format("MMM D, YYYY");
}

/**
 * Formats a UTC timestamp string to a human-readable relative time with full words
 * @param dateString - UTC timestamp string (e.g., "2025-07-21T13:27:25.002746")
 * @returns Formatted time string (e.g., "2 hours ago", "3 days ago", "Just now")
 */
export function formatTimeAgoVerbose(dateString: string): string {
  const date = dayjs.utc(dateString).local();
  const now = dayjs();
  const diffInSeconds = now.diff(date, "second");

  if (diffInSeconds < 60) return "Just now";
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} ${minutes === 1 ? "minute" : "minutes"} ago`;
  }
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} ${hours === 1 ? "hour" : "hours"} ago`;
  }
  if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} ${days === 1 ? "day" : "days"} ago`;
  }

  return date.format("MMM D, YYYY");
}
