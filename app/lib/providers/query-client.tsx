import {
  Query<PERSON><PERSON>,
  Query<PERSON>lient<PERSON><PERSON><PERSON>,
  Query<PERSON>ache,
  MutationCache,
} from "@tanstack/react-query";
import type { PropsWithChildren } from "react";
import Session from "supertokens-web-js/recipe/session";
import { HttpError } from "~/lib/api/types";

// Guard to check if the thrown error is our custom HttpError with a status code
const isHttpError = (error: unknown): error is HttpError => {
  return error instanceof HttpError && typeof error.status === "number";
};

// Centralised handler for 401 auth failures
const handleAuthError = async (error: unknown) => {
  if (isHttpError(error) && error.status === 401) {
    if (typeof window !== "undefined") {
      try {
        // End the SuperTokens session (ignore failures)
        await Session.signOut();
      } catch (e) {
        console.error("signOut error", e);
        // Silent catch – if sign-out fails we still want to clean up locally
      }

      // Remove any persisted app state that is tied to the authenticated user
      localStorage.removeItem("current_group_id");
      localStorage.removeItem("current_cohort_id");

      // Redirect the user to the login page
      window.location.assign("/login");
    }

    // Clear all react-query caches (safe on both client & server)
    queryClient.clear();
  }
};

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        // Don't retry on 401 errors
        if (isHttpError(error) && error.status === 401) {
          return false;
        }
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      refetchOnWindowFocus: true,
      staleTime: 30 * 1000, // 30 seconds
    },
  },
  queryCache: new QueryCache({
    onError: handleAuthError,
  }),
  mutationCache: new MutationCache({
    onError: handleAuthError,
  }),
});

export function QueryProvider({ children }: PropsWithChildren) {
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}
