import { HOSTS_CONFIG } from "~/config/hosts";
import { HttpError } from "~/lib/api/types";
import type {
  APIResponse,
  MyGroupsResponse,
  ProfileResponse,
  GroupsResponse,
  GroupResponse,
  GroupCohortsResponse,
  CohortResponse,
  Module,
  CourseResponse,
  LiveClassesResponse,
  Course,
  LiveClass,
  LessonResponse,
} from "~/lib/api/types";

const API_URL = HOSTS_CONFIG.api;

/**
 * Base fetch function with authentication
 * Handles common headers and error responses
 */
async function fetchWithAuth(
  url: string,
  cookieHeader: string,
  options?: RequestInit
): Promise<Response> {
  const response = await fetch(url, {
    ...options,
    headers: {
      Cookie: cookieHeader,
      ...options?.headers,
    },
  });

  if (!response.ok) {
    throw new HttpError(
      `API request failed: ${response.statusText}`,
      response.status
    );
  }

  return response;
}

/**
 * Generic typed fetch function for API responses
 * Handles JSON parsing and success validation
 */
async function typedFetch<T>(
  url: string,
  cookieHeader: string,
  options?: RequestInit
): Promise<T> {
  const response = await fetchWithAuth(url, cookieHeader, options);
  const data = await response.json();

  // Check for API-level success flag if it exists
  if (typeof data === "object" && "success" in data && !data.success) {
    throw new HttpError(
      data.data?.message || "API request failed",
      response.status
    );
  }

  return data as T;
}

/**
 * Fetch user's joined groups
 * Used in AppLayout and GroupLayout
 */
export async function fetchUserGroups(
  cookieHeader: string
): Promise<MyGroupsResponse> {
  return typedFetch<MyGroupsResponse>(`${API_URL}/me/groups`, cookieHeader);
}

/**
 * Fetch user profile
 * Used in AppLayout
 */
export async function fetchProfile(
  cookieHeader: string
): Promise<ProfileResponse> {
  return typedFetch<ProfileResponse>(`${API_URL}/profile`, cookieHeader);
}

/**
 * Fetch all available groups
 * Used in explore page
 */
export async function fetchGroups(
  cookieHeader: string
): Promise<GroupsResponse> {
  return typedFetch<GroupsResponse>(`${API_URL}/groups`, cookieHeader);
}

/**
 * Fetch specific group details
 * Used in group detail pages
 */
export async function fetchGroup(
  groupId: string,
  cookieHeader: string
): Promise<GroupResponse> {
  return typedFetch<GroupResponse>(
    `${API_URL}/groups/${groupId}`,
    cookieHeader
  );
}

/**
 * Fetch cohorts for a specific group
 * Used in group pages to list available cohorts
 */
export async function fetchGroupCohorts(
  groupId: string,
  cookieHeader: string
): Promise<GroupCohortsResponse> {
  return typedFetch<GroupCohortsResponse>(
    `${API_URL}/groups/${groupId}/cohorts`,
    cookieHeader
  );
}

/**
 * Fetch specific cohort details
 * Used in cohort pages
 */
export async function fetchCohort(
  groupId: string,
  cohortId: string,
  cookieHeader: string
): Promise<CohortResponse> {
  return typedFetch<CohortResponse>(
    `${API_URL}/groups/${groupId}/cohorts/${cohortId}`,
    cookieHeader
  );
}

/**
 * Fetch specific module details
 * Used in module pages
 */
export async function fetchModule(
  groupId: string,
  cohortId: string,
  moduleId: string,
  cookieHeader: string
): Promise<APIResponse<Module>> {
  return typedFetch<APIResponse<Module>>(
    `${API_URL}/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}`,
    cookieHeader
  );
}

/**
 * Fetch course details
 * Used for course modules
 */
export async function fetchCourse(
  courseId: string,
  cookieHeader: string
): Promise<CourseResponse> {
  return typedFetch<CourseResponse>(
    `${API_URL}/courses/${courseId}`,
    cookieHeader
  );
}

/**
 * Fetch lesson details
 * Used in lesson pages
 */
export async function fetchLesson(
  courseId: string,
  sectionId: string,
  lessonId: string,
  cookieHeader: string
): Promise<LessonResponse> {
  return typedFetch<LessonResponse>(
    `${API_URL}/courses/${courseId}/sections/${sectionId}/lessons/${lessonId}`,
    cookieHeader
  );
}

/**
 * Fetch live classes for an events module
 * Used for events modules
 */
export async function fetchLiveClasses(
  groupId: string,
  cohortId: string,
  moduleId: string,
  cookieHeader: string
): Promise<LiveClassesResponse> {
  return typedFetch<LiveClassesResponse>(
    `${API_URL}/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}/live-classes`,
    cookieHeader
  );
}

/**
 * Composite function to fetch module with type-specific data
 * Fetches the module first, then fetches additional data based on module type
 * Used in module pages to reduce loader complexity
 */
export async function fetchModuleWithData(
  groupId: string,
  cohortId: string,
  moduleId: string,
  cookieHeader: string
): Promise<{
  module: Module;
  courseData?: Course;
  liveClasses?: LiveClass[];
}> {
  // First, fetch the module to determine its type
  const moduleResponse = await fetchModule(
    groupId,
    cohortId,
    moduleId,
    cookieHeader
  );

  if (!moduleResponse.success) {
    throw new HttpError("Failed to fetch module data", 500);
  }

  const module = moduleResponse.data;
  let courseData: Course | undefined;
  let liveClasses: LiveClass[] | undefined;

  // Fetch type-specific data based on module type
  try {
    switch (module.type) {
      case "course":
        if (module.config?.courseId) {
          const courseResponse = await fetchCourse(
            String(module.config.courseId),
            cookieHeader
          );
          if (courseResponse.success) {
            courseData = courseResponse.data;
          }
        }
        break;

      case "events":
        const liveClassesResponse = await fetchLiveClasses(
          groupId,
          cohortId,
          moduleId,
          cookieHeader
        );
        if (liveClassesResponse.success) {
          liveClasses = liveClassesResponse.data.liveClasses;
        }
        break;

      // Other module types can be added here as they're implemented
      default:
        // No additional data needed for other module types
        break;
    }
  } catch (error) {
    // Log error but don't fail the entire request
    // The module can still be displayed without type-specific data
  }

  return {
    module,
    courseData,
    liveClasses,
  };
}

/**
 * Utility function to fetch user data in parallel
 * Used in AppLayout for efficient data loading
 */
export async function fetchUserData(cookieHeader: string): Promise<{
  groupsResponse: MyGroupsResponse;
  profileResponse: ProfileResponse;
}> {
  const [groupsResponse, profileResponse] = await Promise.all([
    fetchUserGroups(cookieHeader),
    fetchProfile(cookieHeader),
  ]);

  return {
    groupsResponse,
    profileResponse,
  };
}
