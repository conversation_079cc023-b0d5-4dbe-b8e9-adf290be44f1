import { createContext, useContext, useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router";
import Session from "supertokens-web-js/recipe/session";
import SuperTokens from "supertokens-web-js";
import { authConfig } from "~/config/auth";

// Initialize SuperTokens
if (typeof window !== "undefined") {
  SuperTokens.init(authConfig());
}

interface SessionContextType {
  isLoading: boolean;
  isAuthenticated: boolean;
  checkSession: () => Promise<boolean>;
}

const SessionContext = createContext<SessionContextType | null>(null);

export function useSession() {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error("useSession must be used within SessionProvider");
  }
  return context;
}

export function SessionProvider({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const checkSession = async () => {
    try {
      const doesSessionExist = await Session.doesSessionExist();
      setIsAuthenticated(doesSessionExist);
      return doesSessionExist;
    } catch (error) {
      console.error("Session check error:", error);
      setIsAuthenticated(false);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkSession();
  }, []);

  return (
    <SessionContext.Provider
      value={{ isLoading, isAuthenticated, checkSession }}
    >
      {children}
    </SessionContext.Provider>
  );
}

export function RequireAuth({ children }: { children: React.ReactNode }) {
  const { isLoading, isAuthenticated } = useSession();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // Save the current location so we can redirect back after login
      const from = location.pathname + location.search;
      navigate(`/login?from=${encodeURIComponent(from)}`, { replace: true });
    }
  }, [isLoading, isAuthenticated, navigate, location]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="text-lg text-gray-600 dark:text-gray-400">
            Loading...
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return <>{children}</>;
}

export function RequireNoAuth({ children }: { children: React.ReactNode }) {
  const { isLoading, isAuthenticated } = useSession();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      // If user is already authenticated, redirect to home or the intended destination
      const params = new URLSearchParams(location.search);
      const from = params.get("from") || "/";
      navigate(from, { replace: true });
    }
  }, [isLoading, isAuthenticated, navigate, location]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="text-lg text-gray-600 dark:text-gray-400">
            Loading...
          </div>
        </div>
      </div>
    );
  }

  if (isAuthenticated) {
    return null;
  }

  return <>{children}</>;
}
