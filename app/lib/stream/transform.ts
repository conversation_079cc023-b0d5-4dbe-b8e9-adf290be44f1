import type {
  StreamNotificationGroup,
  StreamActivity,
  StreamActor,
  UINotification,
  ActorSummary,
  PostSummary,
  PostNotification,
  LikeNotification,
  CommentNotification,
  FollowNotification,
  ArrivedNotification,
  GenericNotification,
} from "./types";

const DEFAULT_AVATAR =
  "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiM0Qjc2ODgiLz4KPHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBkPSJNMjAgMjF2LTJhNCA0IDAgMCAwLTQtNEg4YTQgNCAwIDAgMC00IDR2MiIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo8L3N2Zz4KPC9zdmc+";

function extractActorData(actor: StreamActor | string): {
  name: string;
  avatar: string;
} {
  if (typeof actor === "string") {
    return { name: "Someone", avatar: DEFAULT_AVATAR };
  }

  return {
    name: actor.data?.name || "Someone",
    avatar: actor.data?.image || DEFAULT_AVATAR,
  };
}

function createActorSummary(activities: StreamActivity[]): ActorSummary {
  const uniqueActors = new Map<string, { name: string; avatar: string }>();

  activities.forEach((activity) => {
    const actorData = extractActorData(activity.actor);
    const actorId =
      typeof activity.actor === "string" ? activity.actor : activity.actor.id;
    uniqueActors.set(actorId, actorData);
  });

  const actors = Array.from(uniqueActors.values());

  return {
    count: actors.length,
    names: actors.slice(0, 3).map((a) => a.name),
    avatars: actors.slice(0, 3).map((a) => a.avatar),
  };
}

function extractPostSummary(activity: StreamActivity): PostSummary {
  const images =
    (activity.attachments
      ?.filter((att) => att.type === "image")
      .map((att) => att.image_url)
      .filter(Boolean) as string[]) || [];

  return {
    id: activity.id,
    message: activity.message,
    images,
    reactionCounts: activity.reaction_counts,
  };
}

function getCommentPreview(activities: StreamActivity[]): string | undefined {
  // Get the latest comment text from reactions
  for (const activity of activities) {
    const comments = activity.own_reactions?.comment;
    if (comments && comments.length > 0) {
      const latestComment = comments[comments.length - 1];
      return latestComment.data?.text;
    }
  }
  return undefined;
}

export function transformStreamNotification(
  group: StreamNotificationGroup
): UINotification {
  const firstActivity = group.activities[0];
  const actors = createActorSummary(group.activities);

  const base = {
    id: group.id,
    createdAt: group.created_at,
    isRead: group.is_read,
    isSeen: group.is_seen,
  };

  switch (group.verb) {
    case "post": {
      const post = extractPostSummary(firstActivity);
      return {
        ...base,
        verb: "post",
        actors,
        post,
        activityCount: group.activity_count,
      } as PostNotification;
    }

    case "like": {
      const targetPost = extractPostSummary(firstActivity);
      return {
        ...base,
        verb: "like",
        actors,
        targetPost,
      } as LikeNotification;
    }

    case "comment": {
      const targetPost = extractPostSummary(firstActivity);
      const commentPreview = getCommentPreview(group.activities);
      return {
        ...base,
        verb: "comment",
        actors,
        targetPost,
        commentPreview,
      } as CommentNotification;
    }

    case "follow": {
      return {
        ...base,
        verb: "follow",
        actors,
      } as FollowNotification;
    }

    case "arrived": {
      return {
        ...base,
        verb: "arrived",
        actors,
        message: firstActivity.message || "Someone arrived",
      } as ArrivedNotification;
    }

    default: {
      return {
        ...base,
        verb: "generic",
        actors,
        message: firstActivity.message || `New ${group.verb} activity`,
      } as GenericNotification;
    }
  }
}

export function groupNotificationsByDate(
  notifications: UINotification[]
): Record<string, UINotification[]> {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  const lastWeek = new Date(today);
  lastWeek.setDate(lastWeek.getDate() - 7);

  const groups: Record<string, UINotification[]> = {
    Today: [],
    Yesterday: [],
    "This Week": [],
    Earlier: [],
  };

  notifications.forEach((notification) => {
    const notifDate = new Date(notification.createdAt);
    const notifDay = new Date(
      notifDate.getFullYear(),
      notifDate.getMonth(),
      notifDate.getDate()
    );

    if (notifDay.getTime() === today.getTime()) {
      groups["Today"].push(notification);
    } else if (notifDay.getTime() === yesterday.getTime()) {
      groups["Yesterday"].push(notification);
    } else if (notifDate > lastWeek) {
      groups["This Week"].push(notification);
    } else {
      groups["Earlier"].push(notification);
    }
  });

  // Remove empty groups
  Object.keys(groups).forEach((key) => {
    if (groups[key].length === 0) {
      delete groups[key];
    }
  });

  return groups;
}
