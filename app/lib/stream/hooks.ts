import { useState, useEffect, useCallback } from "react";
import { useAppContext } from "~/lib/providers/app-context";
import type { StreamNotificationGroup } from "./types";

export interface NotificationCounts {
  unread: number;
  unseen: number;
  total: number;
}

/**
 * Hook to get notification counts from GetStream
 * Returns unread, unseen, and total notification counts
 */
export function useNotificationCounts() {
  const { streamClient, userId } = useAppContext();
  const [counts, setCounts] = useState<NotificationCounts>({
    unread: 0,
    unseen: 0,
    total: 0,
  });
  const [isLoading, setIsLoading] = useState(false);

  // Function to manually update counts (for immediate UI updates)
  // Supports both relative updates (negative/positive numbers) and absolute updates
  const updateCounts = useCallback(
    (
      updates: Partial<NotificationCounts> & {
        setUnseenToZero?: boolean;
        decrementUnread?: boolean;
      }
    ) => {
      setCounts((prev) => ({
        unread: updates.decrementUnread
          ? Math.max(0, prev.unread - 1)
          : updates.unread !== undefined
          ? Math.max(0, prev.unread + updates.unread)
          : prev.unread,
        unseen: updates.setUnseenToZero
          ? 0
          : updates.unseen !== undefined
          ? Math.max(0, prev.unseen + updates.unseen)
          : prev.unseen,
        total:
          updates.total !== undefined
            ? Math.max(0, prev.total + updates.total)
            : prev.total,
      }));
    },
    []
  );

  const fetchCounts = useCallback(async () => {
    if (!streamClient || !userId) {
      setCounts({ unread: 0, unseen: 0, total: 0 });
      return;
    }

    setIsLoading(true);
    try {
      const notificationFeed = streamClient.feed("notification", userId);
      const response = await notificationFeed.get({
        limit: 100, // Get more notifications to get accurate counts
        withReactionCounts: false, // Don't need reaction counts for counting
        withOwnReactions: false, // Don't need own reactions for counting
      });

      const groups = response.results as StreamNotificationGroup[];

      const unreadCount = groups.filter((group) => !group.is_read).length;
      const unseenCount = groups.filter((group) => !group.is_seen).length;
      const totalCount = groups.length;

      setCounts({
        unread: unreadCount,
        unseen: unseenCount,
        total: totalCount,
      });
    } catch (error) {
      console.error("Error fetching notification counts:", error);
      setCounts({ unread: 0, unseen: 0, total: 0 });
    } finally {
      setIsLoading(false);
    }
  }, [streamClient, userId]);

  useEffect(() => {
    fetchCounts();
  }, [fetchCounts]);

  return {
    counts,
    isLoading,
    refetch: fetchCounts,
    updateCounts,
  };
}

/**
 * Hook to mark notifications as read or seen
 */
export function useNotificationActions() {
  const { streamClient, userId } = useAppContext();

  const markAsRead = useCallback(
    async (notificationIds: string[]) => {
      if (!streamClient || !userId) {
        throw new Error("Stream client or userId not available");
      }

      const notificationFeed = streamClient.feed("notification", userId);
      await notificationFeed.get({
        limit: 1,
        mark_read: notificationIds,
      });
    },
    [streamClient, userId]
  );

  const markAsSeen = useCallback(async () => {
    if (!streamClient || !userId) {
      throw new Error("Stream client or userId not available");
    }

    const notificationFeed = streamClient.feed("notification", userId);
    await notificationFeed.get({
      limit: 1,
      mark_seen: true,
    });
  }, [streamClient, userId]);

  return {
    markAsRead,
    markAsSeen,
  };
}

/**
 * Hook to set up real-time notification feed subscription
 * Returns connection status and last update time
 */
export function useNotificationRealtime() {
  const { streamClient, userId } = useAppContext();
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    if (!streamClient || !userId) {
      setIsConnected(false);
      return;
    }

    let subscription: any = null;

    const setupRealtime = async () => {
      try {
        const notificationFeed = streamClient.feed("notification", userId);

        // Subscribe to real-time updates
        subscription = notificationFeed.subscribe((data: any) => {
          console.log("🔔 Real-time notification update in hook:", data);
          setLastUpdate(new Date());

          // Handle different types of real-time events
          if (data.new && data.new.length > 0) {
            console.log("✨ New notifications in hook:", data.new);
            // The AppContext will handle count updates
            // This hook just tracks connection status
          }

          if (data.deleted && data.deleted.length > 0) {
            console.log("🗑️ Deleted notifications in hook:", data.deleted);
          }

          if (data.updated && data.updated.length > 0) {
            console.log("🔄 Updated notifications in hook:", data.updated);
          }
        });

        setIsConnected(true);
        console.log("🔗 Notification realtime hook connected");
      } catch (error) {
        console.error(
          "❌ Failed to setup notification realtime subscription:",
          error
        );
        setIsConnected(false);
      }
    };

    setupRealtime();

    // Cleanup subscription
    return () => {
      if (subscription && typeof subscription.cancel === "function") {
        subscription.cancel();
        console.log("🔌 Notification realtime hook disconnected");
      }
      setIsConnected(false);
    };
  }, [streamClient, userId]);

  return {
    isConnected,
    lastUpdate,
  };
}
