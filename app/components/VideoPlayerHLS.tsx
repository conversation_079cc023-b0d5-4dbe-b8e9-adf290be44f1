import { useEffect, useRef } from "react";
import Hls from "hls.js";

interface VideoPlayerHLSProps {
  videoUrl: string;
  title?: string;
  className?: string;
}

export function VideoPlayerHLS({
  videoUrl,
  title,
  className = "",
}: VideoPlayerHLSProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<Hls | null>(null);

  useEffect(() => {
    if (!videoUrl || !videoRef.current) return;

    const initializeHLS = () => {
      if (!videoRef.current) {
        return;
      }

      if (Hls.isSupported()) {
        // Destroy existing instance
        if (hlsRef.current) {
          hlsRef.current.destroy();
        }

        const hls = new Hls({
          debug: false,
          enableWorker: true,
          lowLatencyMode: true,
          xhrSetup: function (xhr) {
            // Set CORS headers if needed
            xhr.withCredentials = false;
          },
        });

        hlsRef.current = hls;

        // Bind to video element
        hls.attachMedia(videoRef.current);

        // Load the manifest
        hls.loadSource(videoUrl);

        // Handle events
        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          // Auto-play if needed
          // videoRef.current?.play();
        });

        hls.on(Hls.Events.ERROR, (_event, data) => {
          if (data.fatal) {
            switch (data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                hls.startLoad();
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                hls.recoverMediaError();
                break;
              default:
                hls.destroy();
                break;
            }
          }
        });

        // Level loaded event can be used if needed
      } else if (
        videoRef.current.canPlayType("application/vnd.apple.mpegurl")
      ) {
        // Native HLS support (Safari)
        videoRef.current.src = videoUrl;
      }
    };

    initializeHLS();

    // Cleanup
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };
  }, [videoUrl]);

  return (
    <div className="w-full">
      {title && <h3 className="mb-2 text-lg font-semibold">{title}</h3>}
      <video
        ref={videoRef}
        className={`w-full rounded-lg shadow-lg ${className}`}
        controls
        playsInline
        crossOrigin="anonymous"
      />
    </div>
  );
}
