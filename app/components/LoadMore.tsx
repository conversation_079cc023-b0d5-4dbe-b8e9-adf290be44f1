/**
 * Reusable LoadMore component for infinite scroll pagination
 *
 * @example
 * ```tsx
 * <LoadMore
 *   hasNextPage={hasNextPage}
 *   isFetchingNextPage={isFetchingNextPage}
 *   onLoadMore={() => fetchNextPage()}
 *   buttonText="Load More Items"
 *   loadingText="Loading more items..."
 * />
 * ```
 */

interface LoadMoreProps {
  /** Whether there are more pages to load */
  hasNextPage: boolean;
  /** Whether currently fetching the next page */
  isFetchingNextPage: boolean;
  /** Callback function to load more data */
  onLoadMore: () => void;
  /** Text to show while loading (default: "Loading...") */
  loadingText?: string;
  /** Text to show on the button (default: "Load More") */
  buttonText?: string;
  /** Additional CSS classes for the container */
  className?: string;
}

export function LoadMore({
  hasNextPage,
  isFetchingNextPage,
  onLoadMore,
  loadingText = "Loading...",
  buttonText = "Load More",
  className = "",
}: LoadMoreProps) {
  if (!hasNextPage) {
    return null;
  }

  return (
    <div className={`flex justify-center mt-8 ${className}`}>
      <button
        onClick={onLoadMore}
        disabled={isFetchingNextPage}
        className="px-8 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105 shadow-lg"
      >
        {isFetchingNextPage ? (
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full border-b-2 border-white"></div>
            {loadingText}
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <span>{buttonText}</span>
          </div>
        )}
      </button>
    </div>
  );
}
