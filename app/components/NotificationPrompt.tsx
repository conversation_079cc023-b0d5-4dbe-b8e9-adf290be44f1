import { useState } from "react";
import { Bell, X } from "lucide-react";
import { useAppContext } from "~/lib/providers/app-context";

export function NotificationPrompt() {
  const [isDismissed, setIsDismissed] = useState(false);

  // Add error boundary for context
  let contextData;
  try {
    contextData = useAppContext();
  } catch (error) {
    // If AppProvider is not available, don't render the component
    console.warn(
      "NotificationPrompt: AppProvider not available, skipping render"
    );
    return null;
  }

  const {
    notificationPermission,
    isNotificationSupported,
    requestNotificationPermission,
  } = contextData;

  // Don't show if notifications aren't supported, dismissed, or permission already handled
  if (
    !isNotificationSupported ||
    isDismissed ||
    notificationPermission === "granted" ||
    notificationPermission === "denied"
  ) {
    return null;
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm animate-in slide-in-from-top-2 duration-300">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <Bell className="h-5 w-5 text-blue-500" />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-medium text-gray-900">
              Enable Notifications
            </h3>
            <p className="mt-1 text-xs text-gray-600">
              Stay updated with messages and announcements
            </p>
          </div>
        </div>
        <button
          onClick={() => setIsDismissed(true)}
          className="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
      <div className="mt-3 flex space-x-2">
        <button
          onClick={requestNotificationPermission}
          className="bg-blue-600 text-white px-3 py-1.5 rounded-md text-xs font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
        >
          Allow
        </button>
        <button
          onClick={() => setIsDismissed(true)}
          className="bg-gray-100 text-gray-700 px-3 py-1.5 rounded-md text-xs font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
        >
          Not now
        </button>
      </div>
    </div>
  );
}
