import { Link } from "react-router";
import { User, ArrowLeft } from "lucide-react";
import { useGroup } from "~/lib/api/client-queries";
import type { Cohor<PERSON> } from "~/lib/api/types";

interface InfoModuleProps {
  groupId: string;
  cohort: Cohort;
}

export function InfoModule({ groupId, cohort }: InfoModuleProps) {
  const { data: groupData } = useGroup(groupId);
  const group = groupData?.data;

  const defaultContent = "<h1>Welcome to the Cohort!</h1>";
  const bio = cohort.bio || defaultContent;

  return (
    <div className="flex h-screen bg-zinc-950">
      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Content */}
        <div className="">
          <div className="max-w-4xl mx-auto px-8 py-6">
            {/* Hero Section */}
            <div className="mb-8">
              <p className="text-zinc-500 text-sm font-bold mb-2">ABOUT</p>
              <h1 className="text-2xl font-bold text-white mb-3">
                {cohort.name}
              </h1>
            </div>

            {/* Main Bio Content */}
            <div className="mb-8">
              <div className="bg-zinc-900/50 rounded-xl p-6 border border-zinc-800">
                <div
                  className="prose prose-invert prose-lg max-w-none
                    prose-headings:text-white prose-headings:font-semibold
                    prose-h1:text-2xl prose-h1:mb-4 prose-h1:mt-0
                    prose-h2:text-xl prose-h2:mb-3 prose-h2:mt-6
                    prose-h3:text-lg prose-h3:mb-2 prose-h3:mt-4
                    prose-p:text-zinc-300 prose-p:leading-relaxed prose-p:mb-4 prose-p:text-sm
                    prose-a:text-indigo-400 prose-a:no-underline hover:prose-a:text-indigo-300 hover:prose-a:underline
                    prose-strong:text-white prose-strong:font-semibold
                    prose-ul:my-4 prose-ul:space-y-1
                    prose-ol:my-4 prose-ol:space-y-1
                    prose-li:text-zinc-300 prose-li:leading-relaxed prose-li:text-sm
                    prose-li:marker:text-zinc-500
                    prose-blockquote:border-l-4 prose-blockquote:border-zinc-600 prose-blockquote:pl-4 prose-blockquote:italic prose-blockquote:text-zinc-400
                    prose-code:text-zinc-300 prose-code:bg-zinc-800 prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded prose-code:text-sm
                    prose-pre:bg-zinc-800 prose-pre:text-zinc-300 prose-pre:rounded-lg prose-pre:p-4
                    prose-img:rounded-lg prose-img:my-4
                    prose-hr:border-zinc-700 prose-hr:my-6
                    [&_.my-custom-heading-class]:text-white [&_.my-custom-heading-class]:font-semibold
                    [&_.tiptap-bullet-list]:list-disc [&_.tiptap-bullet-list]:pl-6 [&_.tiptap-bullet-list]:space-y-1
                    [&_.tiptap-ordered-list]:list-decimal [&_.tiptap-ordered-list]:pl-6 [&_.tiptap-ordered-list]:space-y-1
                    [&_.tiptap-list-item]:text-zinc-300 [&_.tiptap-list-item]:leading-relaxed [&_.tiptap-list-item]:text-sm
                    [&_.tiptap-list-item_p]:mb-0"
                  dangerouslySetInnerHTML={{ __html: bio }}
                />
              </div>
            </div>

            {/* About Creator Section */}
            {group && (
              <div className="mb-8">
                <h2 className="text-base font-semibold text-white mb-4">
                  About Creator
                </h2>
                <div className="bg-zinc-900/50 rounded-xl border border-zinc-800 overflow-hidden">
                  <Link
                    to={`/profile/${group.creatorSupertokensUserId}`}
                    className="block p-6 hover:bg-zinc-800/30 transition-colors"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-zinc-700 rounded-full flex items-center justify-center overflow-hidden">
                          {group.creatorAvatarURL ? (
                            <img
                              src={group.creatorAvatarURL}
                              alt={group.creatorName}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <User className="w-6 h-6 text-zinc-400" />
                          )}
                        </div>
                      </div>
                      <div className="flex-1">
                        <h4 className="text-white font-medium text-sm mb-1">
                          {group.creatorName}
                        </h4>
                        {group.creatorBio && (
                          <p className="text-zinc-400 leading-relaxed text-sm">
                            {group.creatorBio}
                          </p>
                        )}
                      </div>
                    </div>
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
