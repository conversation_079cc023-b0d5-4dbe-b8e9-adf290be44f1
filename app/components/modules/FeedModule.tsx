import { useState, useCallback, useRef, useEffect } from "react";
import { useNavigate } from "react-router";
import {
  Loader2,
  MoreHorizontal,
  ImageIcon,
  X,
  Check,
  ZoomIn,
} from "lucide-react";

import type { FeedModule as FeedModuleType } from "~/lib/api/types";
import {
  useProfile,
  useGetUploadUrl,
  useFeedActivities,
  useFeedRealtime,
  useFeedLike,
  useFeedComment,
  useFeedPost,
  useFeedEditPost,
} from "~/lib/api/client-queries";
import { canUserPostToFeed } from "~/lib/utils/feed";
import { FeedPostCard } from "~/components/feed/FeedPostCard";
import { ImageViewer } from "~/components/feed/ImageViewer";
import { useAppContext } from "~/lib/providers/app-context";

// Helper function to check if a file is an image
const isImageFile = (file: File): boolean => {
  return file.type.startsWith("image/");
};

// URL detection regex - matches http/https URLs
const urlRegex = /(https?:\/\/[^\s]+)/g;

// Function to extract URLs from text
const extractUrls = (text: string): string[] => {
  const matches = text.match(urlRegex);
  return matches || [];
};

interface FeedModuleProps {
  module: FeedModuleType;
  params: {
    groupId: string;
    cohortId: string;
    moduleId: string;
  };
}

export function FeedModule({ module, params }: FeedModuleProps) {
  const { data: profileData } = useProfile();
  const { userId, streamClient } = useAppContext();
  const getUploadUrl = useGetUploadUrl();
  const navigate = useNavigate();

  // Use optimized React Query hooks
  const {
    data,
    error,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useFeedActivities(module.config.feedGroup, module.config.feedId);

  // Set up real-time feed subscription
  const { isConnected: isRealtimeConnected, lastUpdate } = useFeedRealtime(
    module.config.feedGroup,
    module.config.feedId
  );

  // Log real-time connection status for debugging
  useEffect(() => {
    console.log(
      `🔄 Real-time connection status: ${
        isRealtimeConnected ? "Connected" : "Disconnected"
      }`
    );
    if (lastUpdate) {
      console.log(
        `📅 Last update received at: ${lastUpdate.toLocaleTimeString()}`
      );
    }
  }, [isRealtimeConnected, lastUpdate]);

  const likeMutation = useFeedLike(
    module.config.feedGroup,
    module.config.feedId
  );
  const commentMutation = useFeedComment(
    module.config.feedGroup,
    module.config.feedId
  );
  const postMutation = useFeedPost(
    module.config.feedGroup,
    module.config.feedId
  );
  const editPostMutation = useFeedEditPost(
    module.config.feedGroup,
    module.config.feedId
  );

  const [postText, setPostText] = useState("");
  const [postImage, setPostImage] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [ogPreview, setOgPreview] = useState<any>(null);
  const [isLoadingOg, setIsLoadingOg] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [mainImageViewerOpen, setMainImageViewerOpen] = useState(false);
  const [mainSelectedImageUrl, setMainSelectedImageUrl] = useState<string>("");
  const [mainSelectedImageAlt, setMainSelectedImageAlt] = useState<string>("");
  const [activityImageViewerOpen, setActivityImageViewerOpen] = useState(false);
  const [activitySelectedImageUrl, setActivitySelectedImageUrl] =
    useState<string>("");
  const [activitySelectedImageAlt, setActivitySelectedImageAlt] =
    useState<string>("");
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Check if user has permission to post
  const userRole = profileData?.data?.role;
  const userOrgId = profileData?.data?.organizationId;
  const canPost = canUserPostToFeed(userRole, userOrgId, module.config.feedId);

  // Get flattened activities from paginated data
  const activities = data?.pages.flatMap((page: any) => page.results) || [];

  // Infinite scroll handler
  const handleScroll = useCallback(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
    const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);

    if (distanceFromBottom <= 200 && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  // Attach scroll listener
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    scrollContainer.addEventListener("scroll", handleScroll, { passive: true });
    return () => scrollContainer.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  const handleLike = useCallback(
    (activityId: string, isLiked: boolean, reactionId?: string) => {
      likeMutation.mutate({ activityId, isLiked, reactionId });
    },
    [likeMutation]
  );

  const handleComment = useCallback(
    (activityId: string, text: string) => {
      commentMutation.mutate({ activityId, text });
    },
    [commentMutation]
  );

  const handleEdit = useCallback(
    async (
      activityId: string,
      text: string,
      attachment?: {
        type: string;
        url: string;
        fileName?: string;
      },
      ogData?: any
    ) => {
      // Find the activity to get its foreign_id and time
      const activity = activities.find((a) => a.id === activityId);
      if (!activity) {
        console.error("Cannot edit post: activity not found");
        throw new Error("Post not found");
      }

      if (!activity.foreign_id || !activity.time) {
        console.error(
          "Cannot edit post: missing foreign_id or time. This post was created before editing was supported."
        );
        throw new Error(
          "This post cannot be edited. Only posts created after the edit feature was added can be modified."
        );
      }

      return editPostMutation.mutateAsync({
        activityId,
        foreignId: activity.foreign_id,
        time: activity.time,
        text,
        attachment,
        ogData,
      });
    },
    [activities, editPostMutation]
  );

  const handleShare = useCallback(
    async (activityId: string) => {
      const postUrl = `${window.location.origin}/groups/${params.groupId}/cohorts/${params.cohortId}/modules/${params.moduleId}/posts/${activityId}`;

      try {
        await navigator.clipboard.writeText(postUrl);
        setShowToast(true);
        setTimeout(() => setShowToast(false), 3000);
      } catch (err) {
        // Fallback for older browsers
        const textArea = document.createElement("textarea");
        textArea.value = postUrl;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        setShowToast(true);
        setTimeout(() => setShowToast(false), 3000);
      }
    },
    [params]
  );

  const handlePostClick = useCallback(
    (activityId: string) => {
      navigate(
        `/groups/${params.groupId}/cohorts/${params.cohortId}/modules/${params.moduleId}/posts/${activityId}`
      );
    },
    [navigate, params]
  );

  const handleMainImageClick = useCallback((imageUrl: string, alt: string) => {
    setMainSelectedImageUrl(imageUrl);
    setMainSelectedImageAlt(alt);
    setMainImageViewerOpen(true);
  }, []);

  const handleCloseMainImageViewer = useCallback(() => {
    setMainImageViewerOpen(false);
    setMainSelectedImageUrl("");
    setMainSelectedImageAlt("");
  }, []);

  const handleActivityImageClick = useCallback(
    (imageUrl: string, alt: string) => {
      setActivitySelectedImageUrl(imageUrl);
      setActivitySelectedImageAlt(alt);
      setActivityImageViewerOpen(true);
    },
    []
  );

  const handleCloseActivityImageViewer = useCallback(() => {
    setActivityImageViewerOpen(false);
    setActivitySelectedImageUrl("");
    setActivitySelectedImageAlt("");
  }, []);

  // Function to fetch Open Graph data
  const fetchOgData = useCallback(
    async (url: string) => {
      if (!streamClient) return null;

      try {
        setIsLoadingOg(true);
        const ogData = await streamClient.og(url);
        return ogData;
      } catch (error) {
        console.error("Error fetching OG data:", error);
        return null;
      } finally {
        setIsLoadingOg(false);
      }
    },
    [streamClient]
  );

  // Handle text change and detect URLs for OG preview
  const handlePostTextChange = useCallback(
    (text: string) => {
      setPostText(text);

      // Clear existing OG preview if text is empty
      if (!text.trim()) {
        setOgPreview(null);
        return;
      }

      // Extract URLs from the text
      const urls = extractUrls(text);

      if (urls.length > 0) {
        // Use the first URL found for OG preview
        const firstUrl = urls[0];

        // Validate that the URL looks complete (basic validation)
        try {
          new URL(firstUrl);
          // URL is valid, clear any existing preview if it's a different URL
          if (!ogPreview || ogPreview.url !== firstUrl) {
            setOgPreview(null);
          }
        } catch {
          // Invalid URL, clear preview
          setOgPreview(null);
        }
      } else {
        // No URLs found, clear preview
        setOgPreview(null);
      }
    },
    [ogPreview]
  );

  // Debounced effect to fetch OG data after user stops typing
  useEffect(() => {
    if (!postText.trim()) return;

    const urls = extractUrls(postText);
    if (urls.length === 0) return;

    const firstUrl = urls[0];

    // Validate URL
    try {
      new URL(firstUrl);
    } catch {
      return;
    }

    // Only fetch if it's a different URL than current preview
    if (ogPreview && ogPreview.url === firstUrl) return;

    const timeoutId = setTimeout(async () => {
      const ogData = await fetchOgData(firstUrl);
      if (ogData) {
        setOgPreview(ogData);
      }
    }, 1000); // Wait 1 second after user stops typing

    return () => clearTimeout(timeoutId);
  }, [postText, ogPreview, fetchOgData]);

  const handleCreatePost = useCallback(async () => {
    if (!postText.trim()) return;

    try {
      let attachment = undefined;

      if (selectedFile) {
        const uploadUrlResponse = await getUploadUrl.mutateAsync({
          contentType: selectedFile.type,
          fileName: selectedFile.name,
        });

        const uploadResponse = await fetch(uploadUrlResponse.uploadUrl, {
          method: "PUT",
          headers: {
            "Content-Type": selectedFile.type,
          },
          body: selectedFile,
        });

        if (!uploadResponse.ok) {
          throw new Error(`Upload failed: ${uploadResponse.statusText}`);
        }

        attachment = {
          type: isImageFile(selectedFile) ? "image" : "file",
          url: uploadUrlResponse.cdnUrl,
          fileName: selectedFile.name,
        };
      }

      await postMutation.mutateAsync({
        text: postText,
        attachment,
        ogData: ogPreview,
      });

      // Clear form
      setPostText("");
      setPostImage(null);
      setSelectedFile(null);
      setOgPreview(null);
    } catch (error) {
      console.error("Error creating post:", error);
    }
  }, [postText, selectedFile, getUploadUrl, postMutation]);

  const handleImageUpload = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        if (!isImageFile(file)) {
          return;
        }

        const maxSize = 100 * 1024 * 1024; // 100MB
        if (file.size > maxSize) {
          return;
        }

        setSelectedFile(file);

        const reader = new FileReader();
        reader.onloadend = () => {
          setPostImage(reader.result as string);
        };
        reader.readAsDataURL(file);
      }
      e.target.value = "";
    },
    []
  );

  return (
    <div className="flex flex-col h-full bg-black">
      {/* Top Bar */}
      <div className="sticky top-0 z-50 bg-black/10 backdrop-blur-md border-b border-zinc-900">
        <div className="max-w-4xl mx-auto px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-white">
                {module.name}
              </h1>
              <div className="flex items-center gap-2">
                <p className="text-sm text-zinc-400">
                  {activities.length} Activities
                </p>
                {!isRealtimeConnected && (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-xs text-red-400">Offline</span>
                  </div>
                )}
              </div>
            </div>
            <button className="p-2 hover:bg-zinc-900 rounded-lg transition-colors">
              <MoreHorizontal className="w-5 h-5 text-zinc-400" />
            </button>
          </div>
        </div>
      </div>

      {/* Feed Content */}
      <div ref={scrollContainerRef} className="flex-1 overflow-auto bg-black">
        <div className="max-w-4xl mx-auto px-8 py-12">
          {/* Create Post Form */}
          {!isLoading && !error && canPost && (
            <div className="bg-zinc-900 rounded-lg shadow-sm border border-zinc-700 p-6 mb-6 max-w-[600px] w-full mx-auto">
              <h3 className="text-lg font-semibold text-white mb-4">
                Create a Post
              </h3>

              <textarea
                value={postText}
                onChange={(e) => handlePostTextChange(e.target.value)}
                placeholder="What's on your mind?"
                className="w-full px-4 py-3 text-white bg-zinc-700 border border-zinc-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none placeholder-zinc-400"
                rows={3}
              />

              {postImage && (
                <div className="mt-3 relative group">
                  <img
                    src={postImage}
                    alt="Upload preview"
                    className="max-h-64 rounded-lg cursor-pointer transition-opacity group-hover:opacity-90"
                    onClick={(e: React.MouseEvent) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleMainImageClick(postImage, "Upload preview");
                    }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/20 rounded-lg pointer-events-none">
                    <ZoomIn className="w-8 h-8 text-white" />
                  </div>
                  <button
                    onClick={() => {
                      setPostImage(null);
                      setSelectedFile(null);
                    }}
                    className="absolute top-2 right-2 p-1 bg-black/50 rounded-full hover:bg-black/70 transition-colors z-10"
                  >
                    <X className="w-4 h-4 text-white" />
                  </button>
                </div>
              )}

              {/* Open Graph Preview */}
              {ogPreview && (
                <div className="mt-3 border border-zinc-600 rounded-lg overflow-hidden bg-zinc-800 relative w-full">
                  {ogPreview.images && ogPreview.images.length > 0 && (
                    <div className="w-full aspect-[1.91/1] bg-zinc-700">
                      <img
                        src={ogPreview.images[0].image}
                        alt={ogPreview.title || "Link preview"}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <div className="p-4">
                    {ogPreview.title && (
                      <h4 className="font-semibold text-white text-sm mb-1 line-clamp-2">
                        {ogPreview.title}
                      </h4>
                    )}
                    {ogPreview.description && (
                      <p className="text-zinc-400 text-sm mb-2 line-clamp-2">
                        {ogPreview.description}
                      </p>
                    )}
                    {ogPreview.url && (
                      <p className="text-blue-400 text-xs truncate">
                        {new URL(ogPreview.url).hostname}
                      </p>
                    )}
                  </div>
                  <button
                    onClick={() => setOgPreview(null)}
                    className="absolute top-2 right-2 p-1 bg-black/50 rounded-full hover:bg-black/70 transition-colors z-10"
                  >
                    <X className="w-4 h-4 text-white" />
                  </button>
                </div>
              )}

              {/* Loading indicator for OG data */}
              {isLoadingOg && (
                <div className="mt-3 flex items-center gap-2 text-zinc-400 text-sm">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Loading link preview...</span>
                </div>
              )}

              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <label className="cursor-pointer">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    <div className="p-2 text-zinc-400 hover:bg-zinc-700 rounded-lg transition-colors">
                      <ImageIcon className="w-5 h-5" />
                    </div>
                  </label>
                </div>

                <button
                  onClick={handleCreatePost}
                  disabled={!postText.trim() || postMutation.isPending}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                >
                  {postMutation.isPending ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      {selectedFile ? "Uploading & Posting..." : "Posting..."}
                    </>
                  ) : (
                    "Post"
                  )}
                </button>
              </div>
            </div>
          )}

          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-300">
              Failed to load feed. Please try again later.
            </div>
          )}

          {/* Empty State */}
          {!isLoading && !error && activities.length === 0 && (
            <div className="text-center py-12">
              <p className="text-zinc-400">
                No activities yet. Check back later!
              </p>
            </div>
          )}

          {/* Activities */}
          {!isLoading && !error && activities.length > 0 && (
            <div className="space-y-4">
              {activities.map((activity) => (
                <FeedPostCard
                  key={activity.id}
                  activity={activity}
                  onLike={handleLike}
                  onComment={handleComment}
                  onShare={handleShare}
                  onEdit={handleEdit}
                  onPostClick={handlePostClick}
                  onImageClick={handleActivityImageClick}
                  currentUserId={userId}
                  isClickable={true}
                />
              ))}
            </div>
          )}

          {/* Loading More */}
          {isFetchingNextPage && (
            <div className="flex justify-center py-8">
              <div className="flex items-center gap-2 text-zinc-400">
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>Loading more activities...</span>
              </div>
            </div>
          )}

          {/* End of Feed */}
          {!isLoading && !error && activities.length > 0 && !hasNextPage && (
            <div className="flex justify-center py-8">
              <div className="text-zinc-500 text-sm">
                You've reached the end of the feed
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed top-4 right-4 bg-zinc-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2">
          <Check className="w-4 h-4" />
          <p className="text-xs">Link copied to clipboard!</p>
        </div>
      )}

      {/* Main Image Viewer Modal */}
      <ImageViewer
        imageUrl={mainSelectedImageUrl}
        alt={mainSelectedImageAlt}
        isOpen={mainImageViewerOpen}
        onClose={handleCloseMainImageViewer}
      />

      {/* Activity Image Viewer Modal */}
      <ImageViewer
        imageUrl={activitySelectedImageUrl}
        alt={activitySelectedImageAlt}
        isOpen={activityImageViewerOpen}
        onClose={handleCloseActivityImageViewer}
      />
    </div>
  );
}
