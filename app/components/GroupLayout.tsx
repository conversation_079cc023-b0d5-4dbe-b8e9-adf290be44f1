import { Outlet, Link, useParams, useLocation } from "react-router";
import { useQueries } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";

import { HOSTS_CONFIG } from "~/config/hosts";
import { useMyGroups } from "~/lib/api/client-queries";
import { HttpError, type Module } from "~/lib/api/types";

export default function GroupLayout() {
  const location = useLocation();

  // Bypass GroupLayout entirely on lesson pages to avoid showing extra sidebar
  const isLessonPage =
    location.pathname.includes("/courses/") &&
    location.pathname.includes("/lessons/");
  if (isLessonPage) {
    return (
      <div className="flex-1 overflow-auto">
        <Outlet />
      </div>
    );
  }

  const params = useParams();
  const { groupId } = params;

  // Fetch the user's groups
  const {
    data: myGroupsResponse,
    isLoading: groupsLoading,
    isError: groupsError,
  } = useMyGroups();

  const group = myGroupsResponse?.groups.byId[groupId ?? ""];

  // Loading / error states
  if (groupsLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-900 text-gray-400">
        Loading group…
      </div>
    );
  }

  if (groupsError || !group) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-900 text-red-500">
        Group not found.
      </div>
    );
  }

  const currentModuleId = params.moduleId;

  // Get current date for the header
  const currentDate = new Date();
  const monthYear = currentDate
    .toLocaleDateString("en-US", { month: "short", year: "numeric" })
    .toUpperCase();

  // Fetch cohort details for each joined cohort
  const cohortQueries = useQueries({
    queries:
      group.joinedCohorts.map((cohort) => ({
        queryKey: ["groups", group.externalId, "cohorts", cohort.externalId],
        queryFn: async () => {
          const response = await fetch(
            `${HOSTS_CONFIG.api}/groups/${group.externalId}/cohorts/${cohort.externalId}`,
            {
              credentials: "include",
            }
          );
          if (!response.ok) {
            throw new HttpError("Failed to fetch cohort", response.status);
          }
          const data = await response.json();
          return data.success ? data : null;
        },
      })) ?? [],
  });

  // Map cohort data with fetched details
  const cohortsWithDetails = group.joinedCohorts.map((cohort, index) => {
    const query = cohortQueries[index].data;

    // Sort modules if they exist
    let sortedDetails = query?.data;
    if (sortedDetails && sortedDetails.modules) {
      sortedDetails = {
        ...sortedDetails,
        modules: [...sortedDetails.modules].sort((a, b) => a.order - b.order),
      };
    }

    return {
      ...cohort,
      details: sortedDetails,
      isLoading: query?.isLoading,
      error: query?.error,
    };
  });

  return (
    <div className="flex h-full">
      {/* Second Sidebar - Cohorts Navigation */}
      <aside className="w-64 bg-black border-r border-zinc-700 flex flex-col overflow-hidden">
        {/* Group Header */}
        <div className="p-3">
          <h2 className="text-white font-semibold">{group.name}</h2>
        </div>

        {/* Cohorts Section */}
        <div className="flex-1 overflow-y-auto p-2">
          {cohortsWithDetails.map((cohort) => {
            const modules = cohort.details?.modules || [];

            // Helper function to get icon based on module type
            const getModuleIcon = (type: string) => {
              switch (type) {
                case "course":
                  return (
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                      />
                    </svg>
                  );
                case "events":
                  return (
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  );
                case "discussion":
                  return (
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"
                      />
                    </svg>
                  );
                case "links":
                  return (
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                      />
                    </svg>
                  );
                case "info":
                  return (
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  );
                default:
                  return (
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  );
              }
            };

            return (
              <div
                key={cohort.id}
                className="border-zinc-900 bg-zinc-900/50 border mb-2 rounded-lg"
              >
                {/* Cohort Header */}
                <div className="p-3 w-full flex items-center justify-between">
                  <h3 className="text-white font-medium text-xs">
                    {cohort.name}
                  </h3>

                  <div className="flex items-center space-x-2">
                    {cohort.details?.newItemsCount > 0 && (
                      <span className="bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">
                        {cohort.details.newItemsCount}
                      </span>
                    )}
                  </div>
                </div>

                {/* Cohort Modules */}
                <div>
                  {cohort.isLoading && (
                    <div className="px-4 py-2 text-zinc-500 text-sm">
                      Loading modules...
                    </div>
                  )}
                  {cohort.error && (
                    <div className="px-4 py-2 text-red-400 text-sm">
                      Failed to load modules
                    </div>
                  )}
                  {!cohort.isLoading && !cohort.error && modules.length > 0 && (
                    <div className="px-2 py-2 space-y-1">
                      {modules.map((module: any) => {
                        const isActive = currentModuleId === String(module.id);

                        return (
                          <Link
                            key={module.id}
                            to={`/groups/${group.externalId}/cohorts/${cohort.externalId}/modules/${module.id}`}
                            className={`flex items-center justify-between text-sm transition-colors px-3 py-2 rounded-md ${
                              isActive
                                ? "bg-white text-zinc-900"
                                : "text-zinc-400 hover:text-white hover:bg-zinc-700"
                            }`}
                          >
                            <div className="flex items-center space-x-3">
                              <div
                                className={`${isActive ? "text-zinc-900" : ""}`}
                              >
                                {getModuleIcon(module.type)}
                              </div>
                              <p className="text-xs font-semibold">
                                {module.name}
                              </p>
                            </div>
                          </Link>
                        );
                      })}
                      <Link
                        to={`/groups/${group.externalId}/cohorts/${cohort.externalId}/info`}
                        className={`flex items-center justify-between text-sm transition-colors px-3 py-2 rounded-md ${
                          location.pathname ===
                          `/groups/${group.externalId}/cohorts/${cohort.externalId}/info`
                            ? "bg-white text-black"
                            : "text-gray-400 hover:text-white hover:bg-gray-700"
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <div
                            className={
                              location.pathname ===
                              `/groups/${group.externalId}/cohorts/${cohort.externalId}/info`
                                ? "text-black"
                                : ""
                            }
                          >
                            {getModuleIcon("info")}
                          </div>
                          <span>Info</span>
                        </div>
                      </Link>
                    </div>
                  )}
                  {!cohort.isLoading &&
                    !cohort.error &&
                    modules.length === 0 && (
                      <div className="px-4 py-2 text-zinc-500 text-sm">
                        No modules available
                      </div>
                    )}
                </div>
              </div>
            );
          })}
        </div>
      </aside>

      {/* Main Content Area */}
      <div className="flex-1 overflow-auto">
        <Outlet />
      </div>
    </div>
  );
}
