import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { <PERSON>, <PERSON>fresh<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, MoreHorizontal } from "lucide-react";
import {
  useNotifications,
  useReadNotification,
} from "~/lib/api/client-queries";
import { useAppContext } from "~/lib/providers/app-context";
import type { Notification } from "~/lib/api/types";
import { LoadMore } from "~/components/LoadMore";
import { NotificationItem } from "~/components/notifications";
import {
  transformStreamNotification,
  groupNotificationsByDate,
} from "~/lib/stream/transform";
import type {
  StreamNotificationGroup,
  UINotification,
} from "~/lib/stream/types";
import { formatTimeAgoVerbose } from "~/lib/utils/time";

interface ExtendedNotification extends Notification {
  type: "activity" | "announcement";
}

interface StreamNotificationExtended {
  id: string;
  type: "activity" | "announcement";
  title: string;
  body: string;
  isRead: boolean;
  createdAt: string;
  data?: {
    link?: string;
  };
}

type CombinedNotification = ExtendedNotification | StreamNotificationExtended;

type TabType = "activities" | "announcements";

interface NotificationItemProps {
  item: CombinedNotification;
}

function LegacyNotificationItem({ item }: NotificationItemProps) {
  const { mutate: markAsRead, isPending } = useReadNotification();

  const handleNotificationClick = () => {
    // Mark notification as read if it's not already read (only for regular notifications)
    if (!item.isRead && "id" in item && typeof item.id === "number") {
      markAsRead({ notificationId: item.id.toString() });
    }

    // Navigate to the link if available
    if (item.data?.link) {
      try {
        // For web app, we can directly navigate or open in same tab
        window.location.href = item.data.link;
      } catch (error) {
        console.error("Failed to navigate to link:", error);
      }
    }
  };

  return (
    <div
      className={`bg-zinc-900 rounded-lg border border-zinc-700 p-4 cursor-pointer transition-all hover:bg-zinc-800/50 ${
        isPending ? "opacity-60" : ""
      }`}
      onClick={handleNotificationClick}
    >
      <div className="flex items-start gap-3">
        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
          <Bell className="w-5 h-5 text-blue-600" />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-white mb-1">{item.title}</h3>
          <p className="text-sm text-zinc-300 mb-2">{item.body}</p>
          <p className="text-xs text-zinc-500">
            {formatTimeAgoVerbose(item.createdAt)}
          </p>
        </div>
        {!item.isRead && (
          <div className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0 mt-2" />
        )}
      </div>
    </div>
  );
}

export function NotificationPage() {
  const [activeTab, setActiveTab] = useState<TabType>("activities");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [streamNotifications, setStreamNotifications] = useState<
    UINotification[]
  >([]);
  const [streamLoading, setStreamLoading] = useState(false);
  const [streamHasNextPage, setStreamHasNextPage] = useState(false);
  const [streamIsFetchingNextPage, setStreamIsFetchingNextPage] =
    useState(false);
  const [streamNextPageParam, setStreamNextPageParam] = useState<
    string | undefined
  >(undefined);
  const {
    data,
    isLoading,
    isError,
    refetch,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useNotifications();
  const { userId, streamClient, updateStreamNotificationCounts } =
    useAppContext();

  // Real-time subscription is handled in AppContext to avoid conflicts

  const fetchStreamNotifications = useCallback(
    async (markSeen: boolean = false, isLoadMore: boolean = false) => {
      if (!streamClient) {
        setStreamNotifications([]);
        setStreamHasNextPage(false);
        setStreamNextPageParam(undefined);
        return;
      }

      if (isLoadMore) {
        setStreamIsFetchingNextPage(true);
      } else {
        setStreamLoading(true);
      }

      try {
        const notificationFeed = streamClient.feed("notification", userId);
        const queryParams = {
          limit: 20, // Reasonable page size for production
          withReactionCounts: true,
          withOwnReactions: true,
          mark_seen: markSeen,
          id_lt: isLoadMore ? streamNextPageParam : undefined,
        };

        const response = await notificationFeed.get(queryParams);
        console.log("Stream notifications", response);

        const streamGroups = response.results as StreamNotificationGroup[];
        const transformedNotifications = streamGroups.map(
          transformStreamNotification
        );

        if (isLoadMore) {
          // Append to existing notifications
          setStreamNotifications((prev) => [
            ...prev,
            ...transformedNotifications,
          ]);
        } else {
          // Replace notifications (initial load or refresh)
          setStreamNotifications(transformedNotifications);
        }

        // Update pagination state - use same logic as working feed pagination
        const hasMore = streamGroups.length >= 20; // If we got full page or more, there might be more
        setStreamHasNextPage(hasMore);

        if (streamGroups.length > 0) {
          // Set next page param to the ID of the last notification
          const lastNotification = streamGroups[streamGroups.length - 1];
          setStreamNextPageParam(lastNotification.id);
        } else {
          setStreamNextPageParam(undefined);
        }

        // If we marked notifications as seen, update the counts
        if (markSeen && !isLoadMore) {
          updateStreamNotificationCounts({ setUnseenToZero: true });
        }
      } catch (error) {
        console.error("Error fetching Stream notifications:", error);
        if (!isLoadMore) {
          setStreamNotifications([]);
          setStreamHasNextPage(false);
          setStreamNextPageParam(undefined);
        }
      } finally {
        if (isLoadMore) {
          setStreamIsFetchingNextPage(false);
        } else {
          setStreamLoading(false);
        }
      }
    },
    [streamClient, userId, streamNextPageParam, updateStreamNotificationCounts]
  );

  // Mark notifications as seen when the page loads (only once)
  useEffect(() => {
    if (streamClient && userId) {
      fetchStreamNotifications(true); // Mark as seen on initial load
    }
  }, [streamClient, userId]); // Only depend on streamClient and userId, not the function

  // Function to mark specific notifications as read
  const markNotificationAsRead = useCallback(
    async (notificationId: string) => {
      if (!streamClient) {
        console.error("Stream client not available");
        return;
      }

      try {
        const notificationFeed = streamClient.feed("notification", userId);
        await notificationFeed.get({
          limit: 1, // We only need to trigger the mark_read, not fetch all notifications
          mark_read: [notificationId],
        });

        // Update local state to reflect the read status
        setStreamNotifications((prev) =>
          prev.map((notification) =>
            notification.id === notificationId
              ? { ...notification, isRead: true }
              : notification
          )
        );

        // Update notification counts immediately for better UX
        const currentNotification = streamNotifications.find(
          (n) => n.id === notificationId
        );

        if (currentNotification && !currentNotification.isRead) {
          updateStreamNotificationCounts({ decrementUnread: true });
        }
      } catch (error) {
        console.error("Error marking notification as read:", error);
      }
    },
    [streamClient, userId, updateStreamNotificationCounts, streamNotifications]
  );

  // Function to load more stream notifications
  const loadMoreStreamNotifications = useCallback(() => {
    if (streamHasNextPage && !streamIsFetchingNextPage) {
      fetchStreamNotifications(false, true);
    }
  }, [streamHasNextPage, streamIsFetchingNextPage, fetchStreamNotifications]);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      // Reset pagination state before refreshing
      setStreamNextPageParam(undefined);
      setStreamHasNextPage(false);

      await Promise.all([
        refetch(), // Refetch notifications
        fetchStreamNotifications(true), // Refetch stream notifications and mark as seen
      ]);
    } catch (error) {
      console.error("Error refreshing notifications:", error);
    } finally {
      setIsRefreshing(false);
    }
  }, [refetch, fetchStreamNotifications]);

  const filteredNotifications = useMemo(() => {
    if (activeTab === "activities") {
      // Activities tab: Show only Stream notifications
      return streamNotifications.sort((a, b) => {
        const dateA = new Date(a.createdAt);
        const dateB = new Date(b.createdAt);
        return dateB.getTime() - dateA.getTime();
      });
    } else {
      // Announcements tab: Show only Sphere API notifications - convert to legacy format
      const sphereNotifications: CombinedNotification[] =
        data?.pages?.flatMap((page) =>
          page.success
            ? page.data.notifications.map((notification) => ({
                ...notification,
                type: "announcement" as const,
              }))
            : []
        ) || [];

      return sphereNotifications.sort((a, b) => {
        const dateA = new Date(a.createdAt);
        const dateB = new Date(b.createdAt);
        return dateB.getTime() - dateA.getTime();
      });
    }
  }, [data, streamNotifications, activeTab]);

  const groupedStreamNotifications = useMemo(() => {
    if (activeTab === "activities") {
      return groupNotificationsByDate(streamNotifications);
    }
    return {};
  }, [streamNotifications, activeTab]);

  const renderEmptyState = () => {
    if (isLoading || streamLoading) {
      return (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-zinc-400">Loading notifications...</p>
        </div>
      );
    }

    if (isError) {
      return (
        <div className="flex flex-col items-center justify-center py-12">
          <AlertTriangle className="w-12 h-12 text-zinc-400 mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">
            Something went wrong
          </h3>
          <p className="text-zinc-400 mb-4">
            We couldn't load your notifications
          </p>
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Bell className="w-12 h-12 text-zinc-400 mb-4" />
        <h3 className="text-lg font-medium text-white mb-2">No News Yet</h3>
        <p className="text-zinc-400">
          We'll notify you as soon as we hear anything
        </p>
      </div>
    );
  };

  const notifications = filteredNotifications;

  return (
    <div className="bg-black min-h-screen">
      {/* Top Bar */}
      <div className="sticky top-0 z-50 bg-black/10 backdrop-blur-md border-b border-zinc-800">
        <div className="max-w-4xl mx-auto px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-white">
                Notifications
              </h1>
              <div className="flex items-center gap-2">
                <p className="text-sm text-zinc-400">
                  {notifications.length}{" "}
                  {notifications.length === 1
                    ? "Notification"
                    : "Notifications"}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="p-2 hover:bg-zinc-800 rounded-lg transition-colors disabled:opacity-50"
              >
                <RefreshCw
                  className={`w-5 h-5 text-zinc-400 ${
                    isRefreshing ? "animate-spin" : ""
                  }`}
                />
              </button>
              <button className="p-2 hover:bg-zinc-800 rounded-lg transition-colors">
                <MoreHorizontal className="w-5 h-5 text-zinc-400" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-8 py-12">
        {/* Filter Tabs */}
        <div className="flex gap-2 mb-8">
          <button
            onClick={() => setActiveTab("activities")}
            className={`px-4 py-1.5 rounded-full text-sm font-medium transition-colors ${
              activeTab === "activities"
                ? "bg-white text-black"
                : "bg-zinc-800 text-zinc-400 hover:text-zinc-300"
            }`}
          >
            Activities
          </button>
          <button
            onClick={() => setActiveTab("announcements")}
            className={`px-4 py-1.5 rounded-full text-sm font-medium transition-colors ${
              activeTab === "announcements"
                ? "bg-white text-black"
                : "bg-zinc-800 text-zinc-400 hover:text-zinc-300"
            }`}
          >
            Announcements
          </button>
        </div>

        {/* Notifications */}
        {notifications.length > 0 ? (
          <div className="space-y-4">
            {activeTab === "activities" ? (
              // Render grouped Stream notifications
              Object.entries(groupedStreamNotifications).map(
                ([dateLabel, notifs]) => (
                  <div key={dateLabel}>
                    <h4 className="text-xs uppercase text-zinc-500 mb-3 font-medium">
                      {dateLabel}
                    </h4>
                    <div className="space-y-3">
                      {notifs.map((notification) => (
                        <NotificationItem
                          key={notification.id}
                          notification={notification}
                          onMarkRead={markNotificationAsRead}
                        />
                      ))}
                    </div>
                  </div>
                )
              )
            ) : (
              // Render legacy announcements
              <div className="space-y-3">
                {(notifications as CombinedNotification[]).map(
                  (notification) => (
                    <LegacyNotificationItem
                      key={
                        typeof notification.id === "string"
                          ? notification.id
                          : notification.id.toString()
                      }
                      item={notification}
                    />
                  )
                )}
              </div>
            )}

            {/* Load More Section */}
            {activeTab === "activities" ? (
              <LoadMore
                hasNextPage={streamHasNextPage}
                isFetchingNextPage={streamIsFetchingNextPage}
                onLoadMore={loadMoreStreamNotifications}
                buttonText="Load More Activities"
                loadingText="Loading more activities..."
                className="pt-6 mt-0"
              />
            ) : (
              <LoadMore
                hasNextPage={hasNextPage}
                isFetchingNextPage={isFetchingNextPage}
                onLoadMore={() => fetchNextPage()}
                buttonText="Load More Announcements"
                loadingText="Loading more announcements..."
                className="pt-6 mt-0"
              />
            )}
          </div>
        ) : (
          renderEmptyState()
        )}
      </div>
    </div>
  );
}
