import { Bell } from "lucide-react";
import type { GenericNotification } from "~/lib/stream/types";
import { AvatarGroup } from "./AvatarGroup";
import { BaseCard } from "./BaseCard";
import { formatTimeAgo } from "~/lib/utils/time";

interface GenericNotificationProps {
  notification: GenericNotification;
  onMarkRead?: (id: string) => void;
}

export function GenericNotification({
  notification,
  onMarkRead,
}: GenericNotificationProps) {
  const { actors, message } = notification;

  const handleClick = () => {
    if (!notification.isRead && onMarkRead) {
      onMarkRead(notification.id);
    }
  };

  const getActorText = () => {
    if (actors.count === 1) {
      return actors.names[0];
    } else if (actors.count === 2) {
      return `${actors.names[0]} and ${actors.names[1]}`;
    } else {
      return `${actors.names[0]} and ${actors.count - 1} others`;
    }
  };

  return (
    <BaseCard isRead={notification.isRead} onClick={handleClick}>
      <div className="relative">
        <AvatarGroup urls={actors.avatars} />
        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-zinc-600 rounded-full flex items-center justify-center border-2 border-black">
          <Bell className="w-3 h-3 text-white" />
        </div>
      </div>

      <div className="flex-1 min-w-0">
        <p className="text-sm text-white mb-2">
          <span className="font-medium">{getActorText()}</span>{" "}
          <span className="text-zinc-400">had new activity</span>
        </p>

        {message && <p className="text-sm text-zinc-300 mb-2">"{message}"</p>}

        <span className="text-xs text-zinc-500">
          {formatTimeAgo(notification.createdAt)}
        </span>
      </div>
    </BaseCard>
  );
}
