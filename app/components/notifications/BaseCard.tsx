import type { ReactNode } from "react";

interface BaseCardProps {
  children: ReactNode;
  isRead: boolean;
  onClick?: () => void;
  className?: string;
}

export function BaseCard({
  children,
  isRead,
  onClick,
  className = "",
}: BaseCardProps) {
  return (
    <div
      className={`
        bg-zinc-900 rounded-lg border border-zinc-700 p-4 
        transition-all hover:bg-zinc-800/50
        ${onClick ? "cursor-pointer" : ""}
        ${className}
      `}
      onClick={onClick}
    >
      <div className="flex items-start gap-3">
        {children}
        {!isRead && (
          <div className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0 mt-2" />
        )}
      </div>
    </div>
  );
}
