import { Heart } from "lucide-react";
import type { LikeNotification } from "~/lib/stream/types";
import { AvatarGroup } from "./AvatarGroup";
import { ImageGrid } from "./ImageGrid";
import { BaseCard } from "./BaseCard";
import { formatTimeAgo } from "~/lib/utils/time";

interface LikeNotificationProps {
  notification: LikeNotification;
  onMarkRead?: (id: string) => void;
}

export function LikeNotification({
  notification,
  onMarkRead,
}: LikeNotificationProps) {
  const { actors, targetPost } = notification;

  const handleClick = () => {
    if (!notification.isRead && onMarkRead) {
      onMarkRead(notification.id);
    }
  };

  const getActorText = () => {
    if (actors.count === 1) {
      return actors.names[0];
    } else if (actors.count === 2) {
      return `${actors.names[0]} and ${actors.names[1]}`;
    } else {
      return `${actors.names[0]} and ${actors.count - 1} others`;
    }
  };

  return (
    <BaseCard isRead={notification.isRead} onClick={handleClick}>
      <div className="relative">
        <AvatarGroup urls={actors.avatars} />
        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center border-2 border-black">
          <Heart className="w-3 h-3 text-white fill-current" />
        </div>
      </div>

      <div className="flex-1 min-w-0">
        <p className="text-sm text-white mb-2">
          <span className="font-medium">{getActorText()}</span>{" "}
          <span className="text-zinc-400">liked your post</span>
        </p>

        <div className="flex items-start gap-3">
          <div className="flex-1">
            {targetPost.message && (
              <p className="text-sm text-zinc-300 mb-2 line-clamp-2">
                "{targetPost.message}"
              </p>
            )}

            <div className="flex items-center justify-between">
              <span className="text-xs text-zinc-500">
                {formatTimeAgo(notification.createdAt)}
              </span>
            </div>
          </div>

          {targetPost.images && targetPost.images.length > 0 && (
            <ImageGrid images={targetPost.images} />
          )}
        </div>
      </div>
    </BaseCard>
  );
}
