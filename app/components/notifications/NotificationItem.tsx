import type { UINotification } from "~/lib/stream/types";
import { PostNotification } from "./PostNotification";
import { LikeNotification } from "./LikeNotification";
import { CommentNotification } from "./CommentNotification";
import { ArrivedNotification } from "./ArrivedNotification";
import { FollowNotification } from "./FollowNotification";
import { GenericNotification } from "./GenericNotification";

interface NotificationItemProps {
  notification: UINotification;
  onMarkRead?: (id: string) => void;
}

export function NotificationItem({
  notification,
  onMarkRead,
}: NotificationItemProps) {
  switch (notification.verb) {
    case "post":
      return (
        <PostNotification notification={notification} onMarkRead={onMarkRead} />
      );

    case "like":
      return (
        <LikeNotification notification={notification} onMarkRead={onMarkRead} />
      );

    case "comment":
      return (
        <CommentNotification
          notification={notification}
          onMarkRead={onMarkRead}
        />
      );

    case "arrived":
      return (
        <ArrivedNotification
          notification={notification}
          onMarkRead={onMarkRead}
        />
      );

    case "follow":
      return (
        <FollowNotification
          notification={notification}
          onMarkRead={onMarkRead}
        />
      );

    case "generic":
    default:
      return (
        <GenericNotification
          notification={notification}
          onMarkRead={onMarkRead}
        />
      );
  }
}
