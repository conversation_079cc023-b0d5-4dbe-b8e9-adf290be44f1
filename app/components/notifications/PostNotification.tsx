import type { PostNotification } from "~/lib/stream/types";
import { AvatarGroup } from "./AvatarGroup";
import { ImageGrid } from "./ImageGrid";
import { BaseCard } from "./BaseCard";
import { formatTimeAgo } from "~/lib/utils/time";

interface PostNotificationProps {
  notification: PostNotification;
  onMarkRead?: (id: string) => void;
}

export function PostNotification({
  notification,
  onMarkRead,
}: PostNotificationProps) {
  const { actors, post, activityCount } = notification;

  const handleClick = () => {
    if (!notification.isRead && onMarkRead) {
      onMarkRead(notification.id);
    }
  };

  const getActorText = () => {
    if (actors.count === 1) {
      return actors.names[0];
    } else if (actors.count === 2) {
      return `${actors.names[0]} and ${actors.names[1]}`;
    } else {
      return `${actors.names[0]} and ${actors.count - 1} others`;
    }
  };

  const getPostText = () => {
    if (activityCount === 1) {
      return "posted";
    } else {
      return `made ${activityCount} posts`;
    }
  };

  return (
    <BaseCard isRead={notification.isRead} onClick={handleClick}>
      <AvatarGroup urls={actors.avatars} />

      <div className="flex-1 min-w-0">
        <p className="text-sm text-white mb-2">
          <span className="font-medium">{getActorText()}</span>{" "}
          <span className="text-zinc-400">{getPostText()}</span>
        </p>

        {post.message && (
          <p className="text-sm text-zinc-300 mb-2 line-clamp-2">
            "{post.message}"
          </p>
        )}

        {post.images && post.images.length > 0 && (
          <ImageGrid images={post.images} className="mb-2" />
        )}

        <div className="flex items-center justify-between">
          <span className="text-xs text-zinc-500">
            {formatTimeAgo(notification.createdAt)}
          </span>
        </div>
      </div>
    </BaseCard>
  );
}
