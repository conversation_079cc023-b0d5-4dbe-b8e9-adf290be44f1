interface ImageGridProps {
  images: string[];
  className?: string;
}

export function ImageGrid({ images, className = "" }: ImageGridProps) {
  if (!images.length) return null;

  const displayImages = images.slice(0, 4);
  const extraCount = images.length - 4;

  if (displayImages.length === 1) {
    return (
      <div className={`relative ${className}`}>
        <img
          src={displayImages[0]}
          alt=""
          className="w-16 h-16 rounded-lg object-cover"
        />
      </div>
    );
  }

  if (displayImages.length === 2) {
    return (
      <div className={`flex gap-1 ${className}`}>
        {displayImages.map((img, index) => (
          <img
            key={index}
            src={img}
            alt=""
            className="w-8 h-8 rounded object-cover"
          />
        ))}
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-2 gap-1 w-16 ${className}`}>
      {displayImages.map((img, index) => (
        <div key={index} className="relative">
          <img src={img} alt="" className="w-7 h-7 rounded object-cover" />
          {index === 3 && extraCount > 0 && (
            <div className="absolute inset-0 bg-black bg-opacity-60 rounded flex items-center justify-center">
              <span className="text-white text-xs font-medium">
                +{extraCount}
              </span>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
