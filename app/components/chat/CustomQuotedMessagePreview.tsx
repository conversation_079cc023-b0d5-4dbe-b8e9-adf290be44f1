import React from "react";
import { Ava<PERSON>, MessageText } from "stream-chat-react";

export const CustomQuotedMessagePreview = (props: any) => {
  // Extract the quoted message from props
  const quotedMessage = props.quotedMessage;

  if (!quotedMessage) {
    return null;
  }

  const quotedSender = quotedMessage.user;
  const quotedSenderName = quotedSender?.name || quotedSender?.id || "Unknown";

  return (
    <div className="custom-quoted-message-preview w-full bg-zinc-800 rounded-lg ">
      {/* Header with sender info */}
      <div className="font-semibold text-xs text-zinc-300">
        {quotedSenderName}
      </div>

      {/* Quoted message content */}
      <div className="custom-quoted-content text-zinc-400 ">
        <MessageText message={quotedMessage} />
      </div>
    </div>
  );
};
