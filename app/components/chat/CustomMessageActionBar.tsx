import React, { useState } from "react";
import { Heart, Reply, MoreHorizontal } from "lucide-react";

interface CustomMessageActionBarProps {
  onReaction: (reactionType: string) => void;
  onReply: () => void;
  onMoreOptions: () => void;
}

export const CustomMessageActionBar = ({
  onReaction,
  onReply,
  onMoreOptions,
}: CustomMessageActionBarProps) => {
  const [showReactionPicker, setShowReactionPicker] = useState(false);

  const reactions = [
    { emoji: "👍", type: "like", label: "Like" },
    { emoji: "❤️", type: "love", label: "Love" },
    { emoji: "😄", type: "haha", label: "Laugh" },
    { emoji: "😮", type: "wow", label: "Wow" },
    { emoji: "😢", type: "sad", label: "Sad" },
  ];

  const handleReactionClick = (
    reactionType: string,
    event: React.MouseEvent
  ) => {
    event.preventDefault();
    event.stopPropagation();
    onReaction(reactionType);
    setShowReactionPicker(false);
  };

  const handleHeartClick = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setShowReactionPicker(!showReactionPicker);
  };

  const handleReplyClick = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    onReply();
  };

  const handleMoreClick = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    onMoreOptions();
  };

  return (
    <div className="custom-message-action-bar">
      {/* Reaction Picker Dropdown */}
      {showReactionPicker && (
        <div className="reaction-picker-dropdown">
          {reactions.map((reaction) => (
            <button
              key={reaction.type}
              onClick={(e) => handleReactionClick(reaction.type, e)}
              className="reaction-picker-item"
              title={reaction.label}
            >
              {reaction.emoji}
            </button>
          ))}
        </div>
      )}

      {/* Action Buttons */}
      <div className="action-buttons">
        {/* Heart - Reactions */}
        <button
          onClick={handleHeartClick}
          className="action-button"
          title="Add reaction"
          type="button"
        >
          <Heart size={16} />
        </button>

        {/* Reply */}
        <button
          onClick={handleReplyClick}
          className="action-button"
          title="Reply"
          type="button"
        >
          <Reply size={16} />
        </button>

        {/* More Options */}
        <button
          onClick={handleMoreClick}
          className="action-button"
          title="More options"
          type="button"
        >
          <MoreHorizontal size={16} />
        </button>
      </div>
    </div>
  );
};
