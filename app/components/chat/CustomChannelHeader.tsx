import { useState } from "react";
import { useChannelStateContext } from "stream-chat-react";
import { UserProfileModal } from "./UserProfileModal";

export const CustomChannelHeader = () => {
  const [showMembersModal, setShowMembersModal] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [showProfileModal, setShowProfileModal] = useState(false);

  try {
    const { channel } = useChannelStateContext();

    // Early return if no channel is selected
    if (!channel) {
      return (
        <div className="bg-black border-b border-gray-800 px-4 py-3">
          <div className="flex items-center gap-3">
            <button className="w-8 h-8 flex items-center justify-center text-white hover:text-gray-300 transition-colors">
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
            <h2 className="text-white text-lg font-semibold">Select a chat</h2>
          </div>
        </div>
      );
    }

    // Get channel name or member names (similar to our channel preview logic)
    const members = Object.values(channel?.state?.members || {});
    const memberCount = members.length;
    const currentUserId = channel?._client?.user?.id;
    const otherMember = members.find(
      (member: any) => member.user_id !== currentUserId
    ) as any;

    const channelName =
      (channel?.data as any)?.name || otherMember?.user?.name || "Chat";
    const memberImage = otherMember?.user?.image;

    // Determine if this is a group chat (show member count only for groups)
    const isGroupChat =
      (channel?.data as any)?.name || // Has custom name = group chat
      memberCount > 2; // More than 2 people = group chat

    return (
      <div className="bg-black border-b border-gray-800 px-4 py-3">
        <div className="flex items-center gap-3">
          {/* Back Arrow */}
          <button className="w-8 h-8 flex items-center justify-center text-white hover:text-gray-300 transition-colors">
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>

          {/* Avatar */}
          <div className="w-10 h-10 rounded-full overflow-hidden flex-shrink-0">
            {memberImage ? (
              <img
                src={memberImage}
                alt={channelName}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-indigo-400 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
                {channelName.charAt(0).toUpperCase()}
              </div>
            )}
          </div>

          {/* Channel Name and Member Count */}
          <div
            className="flex flex-col cursor-pointer"
            onClick={() => isGroupChat && setShowMembersModal(true)}
          >
            <h2 className="text-white text-lg font-semibold leading-none mb-1">
              {channelName}
            </h2>
            {isGroupChat && (
              <span className="text-gray-500 text-xs leading-none hover:text-gray-400 transition-colors">
                {memberCount} {memberCount === 1 ? "member" : "members"}
              </span>
            )}
          </div>
        </div>

        {/* Members List Modal */}
        {showMembersModal && isGroupChat && (
          <div
            className="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50"
            onClick={() => setShowMembersModal(false)}
          >
            <div
              className="bg-zinc-900 rounded-lg p-6 max-w-3xl w-full h-4/5 overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-white text-lg font-semibold">
                  Members ({memberCount})
                </h3>
                <button
                  onClick={() => setShowMembersModal(false)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              <div className="space-y-3">
                {members.map((member: any) => {
                  const user = member.user;
                  const isCurrentUser = user.id === currentUserId;

                  return (
                    <div
                      key={user.id}
                      className="flex items-center gap-3 p-3 rounded-lg hover:bg-zinc-800 transition-colors cursor-pointer"
                      onClick={() => {
                        if (!isCurrentUser) {
                          setSelectedUserId(member.user_id);
                          setShowProfileModal(true);
                        }
                      }}
                    >
                      <div className="w-10 h-10 rounded-full overflow-hidden flex-shrink-0">
                        {user.image ? (
                          <img
                            src={user.image}
                            alt={user.name || user.id}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-indigo-400 to-purple-600 flex items-center justify-center text-white font-semibold text-sm">
                            {(user.name || user.id).charAt(0).toUpperCase()}
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="text-white font-medium">
                            {user.name || user.id}
                          </span>
                          {isCurrentUser && (
                            <span className="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded">
                              You
                            </span>
                          )}
                        </div>
                        {member.role && (
                          <p className="text-xs text-gray-500 capitalize">
                            {member.role}
                          </p>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {/* User Profile Modal */}
        <UserProfileModal
          userId={selectedUserId}
          isOpen={showProfileModal}
          onClose={() => {
            setShowProfileModal(false);
            setSelectedUserId(null);
          }}
        />
      </div>
    );
  } catch (error) {
    // Fallback if useChannelStateContext is not available
    return (
      <div className="bg-black border-b border-gray-800 px-4 py-3">
        <div className="flex items-center gap-3">
          <button className="w-8 h-8 flex items-center justify-center text-white hover:text-gray-300 transition-colors">
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <h2 className="text-white text-lg font-semibold">Chat</h2>
        </div>
      </div>
    );
  }
};
