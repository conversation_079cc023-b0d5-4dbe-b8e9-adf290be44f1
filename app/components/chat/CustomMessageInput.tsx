import React, { useEffect, useState } from "react";
import type { LocalMessage, Message, SendMessageOptions } from "stream-chat";
import { MessageInput } from "stream-chat-react";
import { useChannelStateContext } from "stream-chat-react";

export const CustomMessageInput = (props: any) => {
  const [quotedMessage, setQuotedMessage] = useState<any>(null);
  const { channel } = useChannelStateContext();

  // Submit handler that adds quoted_message_id when present
  const overrideSubmitHandler = async ({
    message,
    sendOptions,
  }: {
    cid: string;
    localMessage: LocalMessage;
    message: Message;
    sendOptions: SendMessageOptions;
  }) => {
    if (!channel) return;

    try {
      const quotedId = quotedMessage?.id;
      if (quotedId) {
        await channel.sendMessage(
          {
            ...message,
            quoted_message_id: quotedId,
          },
          sendOptions
        );
        setQuotedMessage(null); // Clear preview after sending
      } else {
        await channel.sendMessage(message, sendOptions);
      }
    } catch (err) {
      console.error("Failed to send message", err);
    }
  };

  useEffect(() => {
    // Listen for custom quoted message events to show preview UI
    const handleSetQuotedMessage = (event: CustomEvent) => {
      const { message } = event.detail;
      setQuotedMessage(message);
    };

    window.addEventListener(
      "setQuotedMessage",
      handleSetQuotedMessage as EventListener
    );

    return () => {
      window.removeEventListener(
        "setQuotedMessage",
        handleSetQuotedMessage as EventListener
      );
    };
  }, []);

  return (
    <div className="custom-message-input-wrapper">
      {/* Show quoted message preview if exists */}
      {quotedMessage && (
        <div className="quoted-message-input-preview bg-zinc-800 border border-zinc-700 rounded-lg">
          <div className="quoted-preview-header">
            <span className="quoted-preview-label">
              Replying to {quotedMessage.user?.name || quotedMessage.user?.id}
            </span>
            <button
              onClick={() => setQuotedMessage(null)}
              className="quoted-preview-close"
            >
              ×
            </button>
          </div>
          <div className="quoted-preview-content">{quotedMessage.text}</div>
        </div>
      )}

      <MessageInput {...props} overrideSubmitHandler={overrideSubmitHandler} />
    </div>
  );
};
