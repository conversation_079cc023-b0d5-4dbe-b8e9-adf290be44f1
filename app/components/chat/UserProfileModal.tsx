import { useState } from "react";
import { X, MessageCircle } from "lucide-react";
import { useChatContext } from "stream-chat-react";
import { useProfileById } from "~/lib/api/client-queries";
import { useAppContext } from "~/lib/providers/app-context";

interface UserProfileModalProps {
  userId: string | null;
  isOpen: boolean;
  onClose: () => void;
}

export const UserProfileModal = ({
  userId,
  isOpen,
  onClose,
}: UserProfileModalProps) => {
  const { data: profileData, isLoading, error } = useProfileById(userId || "");
  const [isStartingChat, setIsStartingChat] = useState(false);
  const { chatClient } = useAppContext();
  const { setActiveChannel } = useChatContext();

  const handleStartChat = async () => {
    if (!chatClient || !chatClient.user?.id || !userId) return;

    const currentStreamUserId = chatClient.user.id;
    setIsStartingChat(true);

    try {
      // Create or get the direct message channel without specifying ID
      // This allows Stream Chat to find existing channels automatically
      const channel = chatClient.channel("messaging", {
        members: [currentStreamUserId, userId],
      });

      // Watch the channel (creates it if it doesn't exist)
      await channel.watch();

      // Set as active channel
      setActiveChannel(channel);

      // Close the profile modal
      onClose();
    } catch (error) {
      console.error("Failed to start chat:", error);
    } finally {
      setIsStartingChat(false);
    }
  };

  if (!isOpen || !userId) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
      onClick={onClose}
    >
      <div
        className="bg-zinc-900 rounded-lg border border-gray-800 w-full max-w-md mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-800">
          <h2 className="text-xl font-semibold text-white">Profile</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {isLoading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-400"></div>
            </div>
          )}

          {error && (
            <div className="flex items-center justify-center py-8">
              <p className="text-gray-400">Failed to load profile</p>
            </div>
          )}

          {profileData?.data && (
            <div className="space-y-6">
              {/* Avatar and Name */}
              <div className="flex flex-col items-center text-center">
                <div className="w-20 h-20 rounded-full overflow-hidden mb-4">
                  {profileData.data.avatarUrl ? (
                    <img
                      src={profileData.data.avatarUrl}
                      alt={`${profileData.data.firstName} ${profileData.data.lastName}`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-indigo-400 to-purple-600 flex items-center justify-center text-white font-semibold text-lg">
                      {(
                        profileData.data.firstName?.charAt(0) ||
                        profileData.data.email?.charAt(0) ||
                        "U"
                      ).toUpperCase()}
                    </div>
                  )}
                </div>

                <h3 className="text-xl font-bold text-white mb-1">
                  {`${profileData.data.firstName || ""} ${
                    profileData.data.lastName || ""
                  }`.trim() || "Unnamed User"}
                </h3>

                <p className="text-gray-400 text-sm">
                  {profileData.data.email}
                </p>
              </div>

              {/* Details */}
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-300 mb-1">
                    Role
                  </h4>
                  <p className="text-white">
                    {profileData.data.role || "User"}
                  </p>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-300 mb-1">
                    User ID
                  </h4>
                  <p className="text-gray-400 text-sm font-mono">
                    {profileData.data.id}
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className="pt-4 border-t border-gray-800 space-y-3">
                <button
                  onClick={handleStartChat}
                  disabled={isStartingChat}
                  className="w-full px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
                >
                  {isStartingChat ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <MessageCircle className="w-4 h-4" />
                  )}
                  {isStartingChat ? "Starting Chat..." : "Start Chat"}
                </button>
                <button
                  onClick={onClose}
                  className="w-full px-4 py-2 bg-zinc-800 hover:bg-zinc-700 text-white rounded-md transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
