export const CustomChannelPreview = (props: any) => {
  const { channel, setActiveChannel, activeChannel } = props;

  const isActive = activeChannel?.id === channel.id;
  const unreadCount = channel.state.unreadCount || 0;
  const lastMessage = channel.state.messages[channel.state.messages.length - 1];

  // Detect if it's a group chat or private message
  const members = Object.values(channel.state.members || {});
  const memberCount = members.length;
  const currentUserId = channel._client.user?.id;

  // Logic to determine channel type
  const isGroupChat =
    channel.data?.name || // Has custom name = group chat
    memberCount > 2 || // More than 2 people = group chat
    false;

  const isPrivateMessage = memberCount === 2 && !channel.data?.name;

  // Get channel name or member names
  const channelName =
    channel.data?.name ||
    Object.values(channel.state.members || {})
      .filter((member: any) => member.user_id !== currentUserId)
      .map((member: any) => member.user?.name || member.user_id)
      .join(", ") ||
    "Unnamed Channel";

  // Format last message time
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    if (diff < 60000) return "now";
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h`;
    return `${Math.floor(diff / 86400000)}d`;
  };

  return (
    <div
      className={`w-full p-4 mb-1 cursor-pointer transition-colors duration-150 border-none rounded-lg hover:bg-zinc-900 ${
        isActive ? "bg-zinc-900" : ""
      }`}
      onClick={() => setActiveChannel(channel)}
    >
      <div className="flex items-center gap-3 w-full">
        {/* Avatar - Round for private messages, rounded corners for groups */}
        <div
          className={`w-10 h-10 ${
            isPrivateMessage ? "rounded-full" : "rounded"
          } overflow-hidden flex-shrink-0`}
        >
          {channel.data?.image ? (
            <img
              src={channel.data.image}
              alt={channelName}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-indigo-400 to-purple-600 flex items-center justify-center text-white font-semibold text-lg">
              {channelName.charAt(0).toUpperCase()}
            </div>
          )}
        </div>

        {/* Channel Info */}
        <div className="flex-1 min-w-0 flex flex-col gap-1">
          <div className="flex justify-between items-center w-full">
            <h3 className="text-white text-sm font-semibold overflow-hidden text-ellipsis whitespace-nowrap flex-1">
              {channelName}
            </h3>

            {lastMessage && (
              <span className="text-gray-500 text-sm ml-3 font-normal flex-shrink-0">
                {formatTime(lastMessage.created_at)}
              </span>
            )}
          </div>

          <div className="flex justify-between items-center w-full">
            <p className="text-gray-500 text-xs overflow-hidden text-ellipsis whitespace-nowrap flex-1 font-normal">
              {lastMessage?.text || "No messages yet"}
            </p>

            {unreadCount > 0 && (
              <span className="bg-red-500 text-white rounded-full min-w-6 h-6 flex items-center justify-center text-xs font-bold ml-3 flex-shrink-0">
                {unreadCount}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
