import React from "react";
import {
  MessageText,
  MessageTimestamp,
  Avatar,
  MessageActions,
  MessageRepliesCountButton,
  ReactionsList,
  MessageStatus,
  useMessageContext,
  useChannelActionContext,
  useChannelStateContext,
} from "stream-chat-react";
import { useAppContext } from "~/lib/providers/app-context";
import { CustomQuotedMessagePreview } from "./CustomQuotedMessagePreview";
import { CustomMessageActionBar } from "./CustomMessageActionBar";
import { MessageStatus as CustomMessageStatus } from "./MessageStatus";

export const CustomMessage = (props: any) => {
  // Get message data using Stream Chat hooks
  const messageContext = useMessageContext();
  const { message, groupStyles, lastReceivedId, groupedByUser } =
    messageContext;

  // Get the official action functions from Stream Chat
  const { openThread } = useChannelActionContext();
  const channelStateContext = useChannelStateContext();

  // Get current user ID from app context
  const { userId } = useAppContext();

  // Calculate isMyMessage correctly
  const isMyMessage = message?.user?.id === userId;

  // Check if message has thread replies
  const hasReplies = message?.reply_count && message.reply_count > 0;

  // Function to handle opening thread (following the official pattern)
  const handleOpenThread = (event: React.MouseEvent) => {
    try {
      // Use the official Stream Chat openThread function
      openThread(message, event);
    } catch (error) {
      console.error("Error opening thread:", error);
    }
  };

  // Function to handle emoji reactions
  const handleReaction = async (emojiType: string) => {
    try {
      const channel = channelStateContext?.channel;
      if (!channel) {
        throw new Error("No channel available");
      }

      // Check if user already reacted with this emoji
      const existingReaction = message.own_reactions?.find(
        (reaction: any) => reaction.type === emojiType
      );

      if (existingReaction) {
        // Remove reaction if it exists
        await channel.deleteReaction(message.id, emojiType);
      } else {
        // Add new reaction
        await channel.sendReaction(message.id, { type: emojiType });
      }
    } catch (error) {
      console.error("Error handling reaction:", error);
      console.error("Channel state:", channelStateContext);
      console.error("Message:", message);

      // Show user-friendly error message
      alert(
        `Failed to ${
          message.own_reactions?.find((r) => r.type === emojiType)
            ? "remove"
            : "add"
        } reaction. Please try again.`
      );
    }
  };

  // Function to handle reply action
  const handleReply = () => {
    try {
      // Dispatch custom event to set quoted message for UI preview
      window.dispatchEvent(
        new CustomEvent("setQuotedMessage", {
          detail: { message },
        })
      );

      // Focus on the message input
      setTimeout(() => {
        const messageInput = document.querySelector(
          ".str-chat__textarea textarea"
        ) as HTMLTextAreaElement;
        if (messageInput) {
          messageInput.focus();
        }
      }, 100);
    } catch (error) {
      console.error("Error setting up reply:", error);
    }
  };

  // Function to handle more options
  const handleMoreOptions = () => {
    const options = [];

    // Add copy option
    options.push("Copy Message");

    // Add edit/delete for own messages
    if (isMyMessage) {
      options.push("Edit Message");
      options.push("Delete Message");
    } else {
      options.push("Report Message");
    }

    // Simple prompt-based menu for now
    const choice = prompt(
      `Choose an option for this message:\n\n` +
        options.map((opt, i) => `${i + 1}. ${opt}`).join("\n") +
        `\n\nEnter option number (1-${options.length}):`
    );

    const choiceNum = parseInt(choice || "0");
    const selectedOption = options[choiceNum - 1];

    switch (selectedOption) {
      case "Copy Message":
        navigator.clipboard
          .writeText(message.text || "")
          .then(() => {
            alert("Message copied to clipboard!");
          })
          .catch(() => {
            alert("Failed to copy message");
          });
        break;

      case "Edit Message":
        const newText = prompt("Edit message:", message.text || "");
        if (newText && newText !== message.text) {
          channelStateContext?.channel
            ?.updateMessage({
              ...message,
              text: newText,
            })
            .then(() => {})
            .catch((error) => {
              console.error("Failed to update message:", error);
              alert("Failed to update message");
            });
        }
        break;

      case "Delete Message":
        if (confirm("Are you sure you want to delete this message?")) {
          channelStateContext?.channel
            ?.deleteMessage(message.id)
            .then(() => {})
            .catch((error) => {
              console.error("Failed to delete message:", error);
              alert("Failed to delete message");
            });
        }
        break;

      case "Report Message":
        const reason = prompt("Report reason (optional):");
        alert("Message reported successfully");
        break;

      default:
        if (choice) {
          alert("Invalid option selected");
        }
        break;
    }
  };

  // Don't render if no message
  if (!message) return null;

  // Get sender info
  const sender = message.user;
  const senderName = sender?.name || sender?.id || "Unknown";

  // Custom grouping logic to match Expo behavior
  const messages = channelStateContext?.channel?.state?.messages || [];
  const currentMessageIndex = messages.findIndex(
    (msg) => msg.id === message.id
  );
  const previousMessage =
    currentMessageIndex > 0 ? messages[currentMessageIndex - 1] : null;
  const nextMessage =
    currentMessageIndex < messages.length - 1
      ? messages[currentMessageIndex + 1]
      : null;

  // Custom grouping logic - more permissive than Stream's default (5 minutes instead of ~1 minute)
  const shouldGroupWithPrevious =
    previousMessage &&
    previousMessage.user?.id === message.user?.id &&
    new Date(message.created_at).getTime() -
      new Date(previousMessage.created_at).getTime() <
      300000; // 5 minutes

  const shouldGroupWithNext =
    nextMessage &&
    nextMessage.user?.id === message.user?.id &&
    new Date(nextMessage.created_at).getTime() -
      new Date(message.created_at).getTime() <
      300000; // 5 minutes

  // Determine custom group position
  const customIsFirstInGroup = !shouldGroupWithPrevious;
  const customIsLastInGroup = !shouldGroupWithNext;
  const customIsGroupedMessage = shouldGroupWithPrevious;

  // Use custom grouping instead of Stream's groupStyles
  const isFirstInGroup = customIsFirstInGroup;
  const isLastInGroup = customIsLastInGroup;
  const isGroupedMessage = customIsGroupedMessage;

  // Debug logging to compare Stream vs Custom grouping

  // Message positioning classes
  let messageContainerClass = isMyMessage
    ? "custom-message-container custom-message-container--own"
    : "custom-message-container custom-message-container--other";

  // Add grouping classes for better spacing
  if (isGroupedMessage) {
    messageContainerClass += " grouped";
  }
  if (isFirstInGroup) {
    messageContainerClass += " group-first";
  }
  if (isLastInGroup) {
    messageContainerClass += " group-last";
  }

  return (
    <div className={`${messageContainerClass}`}>
      {/* Avatar - only show for other users and first message in group */}
      {!isMyMessage && isFirstInGroup && (
        <div className="custom-message-avatar">
          <Avatar image={sender?.image} name={senderName} />
        </div>
      )}

      {/* Message Content Wrapper */}
      <div className="relative  max-w-[70%] min-w-0">
        {/* Sender Name - only show for other users and first message in group */}
        <div className="custom-message-content bg-zinc-900 rounded-lg p-2">
          {!isMyMessage && isFirstInGroup && (
            <div className="custom-message-sender-name text-xs mb-1">
              {senderName}
            </div>
          )}

          {/* Custom Quoted Message Preview */}
          {message.quoted_message && (
            <CustomQuotedMessagePreview
              quotedMessage={message.quoted_message}
            />
          )}

          {/* Custom Message Action Bar - appears on hover */}
          <CustomMessageActionBar
            onReaction={(reactionType) => handleReaction(reactionType)}
            onReply={handleReply}
            onMoreOptions={handleMoreOptions}
          />

          {/* Message Bubble */}
          <div className="custom-message-bubble flex items-end justify-between gap-4 w-full">
            {/* Message Text Content */}
            <div className="custom-message-text">
              <MessageText {...props} />
            </div>
            <div className="custom-message-timestamp shrink-0">
              {/* Message Status (delivered, read, etc.) - only for own messages */}
              <div className="flex gap-1">
                {isMyMessage && (
                  <div className="custom-message-status">
                    <CustomMessageStatus message={message} />
                  </div>
                )}
                <MessageTimestamp
                  message={message}
                  calendar={false}
                  format="h:mm A"
                  customClass="text-[10px]"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Thread Replies Count - Custom button with official openThread */}

        {/* Emoji Reaction Buttons */}
        {/* <div className="custom-message-reaction-picker">
          <button
            onClick={(e) => handleReaction('like', e)}
            className="reaction-button"
            title="Like"
          >
            👍
          </button>
          <button
            onClick={(e) => handleReaction('love', e)}
            className="reaction-button"
            title="Love"
          >
            ❤️
          </button>
          <button
            onClick={(e) => handleReaction('haha', e)}
            className="reaction-button"
            title="Laugh"
          >
            😄
          </button>
          <button
            onClick={(e) => handleReaction('wow', e)}
            className="reaction-button"
            title="Wow"
          >
            😮
          </button>
          <button
            onClick={(e) => handleReaction('sad', e)}
            className="reaction-button"
            title="Sad"
          >
            😢
          </button>
        </div> */}

        {/* Reactions - positioned at bottom like WhatsApp */}
        {!!(
          message.latest_reactions && message.latest_reactions.length > 0
        ) && (
          <div className="custom-message-reactions-host">
            <ReactionsList {...props} />
          </div>
        )}
        {!!hasReplies && (
          <div
            className="custom-message-replies pl-4 relative "
            onClick={handleOpenThread}
          >
            <div className="absolute w-full h-1/2 border-l rounded-bl-lg border-zinc-600 top-0 left-2"></div>
            <div className="text-xs cursor-pointer font-semibold text-zinc-500 inline-block">
              {message.reply_count}{" "}
              {message.reply_count === 1 ? "reply" : "replies"}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
