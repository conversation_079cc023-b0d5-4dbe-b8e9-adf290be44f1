import { PlayCircle, FileText, Headphones } from "lucide-react";
import type { Course, Section, Lesson } from "~/lib/api/types";

interface CourseTOCProps {
  course: Course;
  compact?: boolean;
}

function getLessonIcon(lesson: Lesson) {
  const contentTypes =
    lesson.lessonContents?.map((content) => content.contentType) || [];

  if (contentTypes.includes("video")) {
    return <PlayCircle className="w-5 h-5" />;
  }
  if (contentTypes.includes("audio")) {
    return <Headphones className="w-5 h-5" />;
  }
  return <FileText className="w-5 h-5" />;
}

function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
    .toString()
    .padStart(2, "0")}`;
}

export function CourseTOC({ course, compact = false }: CourseTOCProps) {
  if (!course.sections || course.sections.length === 0) {
    return <p className="text-gray-400">No course content available</p>;
  }

  return (
    <div className="space-y-8">
      {course.sections.map((section, sectionIndex) => {
        const sectionLessons = section.lessons || [];

        return (
          <div key={section.id}>
            <h3 className="text-lg font-semibold mb-4">
              {sectionIndex + 1}. {section.name}
            </h3>

            <div className="space-y-3">
              {sectionLessons.map((lesson) => (
                <div
                  key={lesson.id}
                  className="flex items-center justify-between group cursor-pointer hover:bg-gray-800/30 -mx-6 px-6 py-2 rounded transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-gray-500 group-hover:text-gray-400">
                      {getLessonIcon(lesson)}
                    </span>
                    <span className="text-gray-300 group-hover:text-white">
                      {lesson.title}
                    </span>
                  </div>
                  {/* TODO: Add duration when API provides it */}
                </div>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
}
