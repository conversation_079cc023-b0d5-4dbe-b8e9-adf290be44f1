/* ===== CUSTOM MESSAGE COMPONENT STYLES ===== */

/* Message Container - Main wrapper for each message */
.custom-message-container {
  position: relative;
  display: flex;
  margin-bottom: var(--expo-spacing-md, 12px);
  padding: 0 var(--expo-spacing-md, 12px);
  align-items: flex-start;
  gap: var(--expo-spacing-sm, 8px);
}

/* Grouped messages - reduced spacing */
.custom-message-container.grouped {
  margin-bottom: var(--expo-spacing-xs, 4px);
}

/* First message in group - normal spacing */
.custom-message-container.group-first {
  margin-bottom: var(--expo-spacing-xs, 4px);
}

/* Last message in group - extra spacing after */
.custom-message-container.group-last {
  margin-bottom: var(--expo-spacing-lg, 16px);
}

/* Other user's messages - left aligned */
.custom-message-container--other {
  justify-content: flex-start;
}

/* Own messages - right aligned */
.custom-message-container--own {
  justify-content: flex-end;
}

/* Avatar Container */
.custom-message-avatar {
  flex-shrink: 0;
  width: var(--expo-avatar-medium, 50px);
  height: var(--expo-avatar-medium, 50px);
}

/* Grouped messages from others need left margin to align with avatar */
.custom-message-container--other.grouped {
  margin-left: calc(
    var(--expo-avatar-medium, 50px) + var(--expo-spacing-sm, 8px)
  );
}

/* Message Content Wrapper - contains everything except avatar */
.custom-message-content {
  display: flex;
  flex-direction: column;
}

/* Own messages - align content to the right */
.custom-message-container--own .custom-message-content {
  align-items: flex-end;
}

/* Other messages - align content to the left */
.custom-message-container--other .custom-message-content {
  align-items: flex-start;
}

/* Sender Name */
.custom-message-sender-name {
  font-size: var(--expo-font-size-xs, 12px);
  font-weight: var(--expo-font-weight-bold, 700);
  color: var(--expo-text, white);
}

/* Message Bubble */
.custom-message-bubble {
  position: relative;
  background-color: var(--expo-secondary, #1a1a1a);
  max-width: 100%;
  word-wrap: break-word;
}

/* Own message bubble - different color if needed */
.custom-message-container--own .custom-message-bubble {
  background-color: var(--expo-secondary, #1a1a1a);
}

/* Message Text Content */
.custom-message-text {
  color: var(--expo-text, white);
  font-size: var(--expo-font-size-sm, 14px);
  line-height: 1.4;
  margin: 0;
}

/* Remove default Stream Chat message styling when using custom component */
.custom-message-text .str-chat__message-text {
  background: none !important;
  padding: 0 !important;
  border-radius: 0 !important;
  margin: 0 !important;
  font-size: inherit !important;
  line-height: inherit !important;
  color: inherit !important;
}

/* Message Metadata Row - timestamp and status */
.custom-message-metadata {
}

/* Own messages - reverse the order (status first, then timestamp) */
.custom-message-container--own .custom-message-metadata {
  flex-direction: row-reverse;
}

/* Timestamp */
.custom-message-timestamp {
  font-size: var(--expo-font-size-xs, 12px);
  color: var(--expo-text-secondary, #72767e);
}

/* Hide default Stream Chat timestamp */
.custom-message-timestamp .str-chat__message-simple-timestamp {
  font-size: inherit !important;
  color: inherit !important;
}

/* Message Status (delivered, read indicators) */
.custom-message-status {
  display: flex;
  align-items: center;
  font-size: var(--expo-font-size-xs, 10px);
}

/* Override Stream Chat's default message status styling */
.custom-message-status .str-chat__message-status {
  color: #9ca3af !important; /* gray-400 */
  font-size: 10px !important;
}

/* Style icons within message status - target Lucide icons and SVG elements */
.custom-message-status svg {
  width: 12px !important;
  height: 12px !important;
  stroke-width: 2 !important;
}

/* Style old Stream Chat icons if they still exist */
.custom-message-status .str-chat__message-status svg,
.custom-message-status .str-chat__message-status i {
  width: 12px !important;
  height: 12px !important;
}

/* Delivered status - single checkmark */
.custom-message-status .str-chat__message-status--delivered {
  color: #6b7280 !important; /* gray-500 */
}

.custom-message-status .str-chat__message-status--delivered svg,
.custom-message-status .str-chat__message-status--delivered i {
  stroke: #6b7280 !important; /* gray-500 */
  fill: #6b7280 !important; /* gray-500 */
}

/* Read status - double checkmark or read indicator */
.custom-message-status .str-chat__message-status--read {
  color: #3b82f6 !important; /* blue-500 - WhatsApp blue */
}

.custom-message-status .str-chat__message-status--read svg,
.custom-message-status .str-chat__message-status--read i {
  stroke: #3b82f6 !important; /* blue-500 - WhatsApp blue */
  fill: #3b82f6 !important; /* blue-500 - WhatsApp blue */
}

/* Pending/sending status */
.custom-message-status .str-chat__message-status--pending {
  color: #9ca3af !important; /* gray-400 */
  opacity: 0.6;
}

.custom-message-status .str-chat__message-status--pending svg,
.custom-message-status .str-chat__message-status--pending i {
  stroke: #9ca3af !important; /* gray-400 */
  fill: #9ca3af !important; /* gray-400 */
  opacity: 0.6;
}

/* Failed status */
.custom-message-status .str-chat__message-status--failed {
  color: #ef4444 !important; /* red-500 */
}

.custom-message-status .str-chat__message-status--failed svg,
.custom-message-status .str-chat__message-status--failed i {
  stroke: #ef4444 !important; /* red-500 */
  fill: #ef4444 !important; /* red-500 */
}

/* Custom checkmark styling to match WhatsApp */
.custom-message-status .str-chat__message-status::before {
  content: "";
  /* Add custom checkmark if needed */
}

/* Emoji Reaction Picker */
.custom-message-reaction-picker {
  display: flex;
  gap: var(--expo-spacing-xs, 4px);
  margin-top: var(--expo-spacing-xs, 4px);
  padding: var(--expo-spacing-xs, 4px);
  background-color: var(--expo-tertiary, #4a4a4a);
  border-radius: var(--expo-border-radius-medium, 12px);
  border: 1px solid var(--expo-border-secondary, #4a4a4a);
}

.reaction-button {
  background: none;
  border: none;
  font-size: 16px;
  padding: var(--expo-spacing-xs, 4px);
  border-radius: var(--expo-border-radius-small, 8px);
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reaction-button:hover {
  background-color: var(--expo-secondary, #1a1a1a);
  transform: scale(1.1);
}

.reaction-button:active {
  transform: scale(0.95);
}

/* Custom Quoted Message Preview Styling */
.custom-quoted-message-preview {
  padding: var(--expo-spacing-sm, 8px);
  margin-bottom: var(--expo-spacing-sm, 8px);
}

.custom-quoted-content {
  font-size: var(--expo-font-size-sm, 14px);
  line-height: 1.4;
}

/* Style message text inside custom quoted preview */
.custom-quoted-content .str-chat__message-text {
  background: none !important;
  padding: 0 !important;
  margin: 0 !important;
  color: inherit !important;
  font-size: inherit !important;
  border-radius: 0 !important;
}

/* Hide original quoted message preview */
.str-chat__quoted-message-preview {
  display: none !important;
}

/* Custom Message Action Bar */
.custom-message-action-bar {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  z-index: 10;
  right: 0;
  display: inline-block;
}

/* Position action bar on the right for other users' messages */
.custom-message-container--other .custom-message-action-bar {
  right: 0px;
  transform: translateX(110%) translateY(-50%);
}

/* Position action bar on the left for own messages */
.custom-message-container--own .custom-message-action-bar {
  left: 0px;
  transform: translateX(-100%) translateY(-50%);
}

/* Show action bar on message hover */
.custom-message-container:hover .custom-message-action-bar {
  opacity: 1;
  pointer-events: auto;
}

/* Action buttons container */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: var(--expo-spacing-xs, 4px);
  padding: var(--expo-spacing-xs, 4px);
}

/* Individual action button */
.action-button {
  background: none;
  border: none;
  color: var(--expo-text-secondary, #72767e);
  padding: var(--expo-spacing-xs, 4px);
  border-radius: var(--expo-border-radius-small, 8px);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  z-index: 20;
}

.action-button:hover {
  background-color: var(--expo-tertiary, #4a4a4a);
  color: var(--expo-text, white);
  transform: scale(1.1);
}

/* Reaction picker dropdown */
.reaction-picker-dropdown {
  position: absolute;
  bottom: 100%;
  margin-bottom: var(--expo-spacing-xs, 4px);
  display: flex;
  gap: var(--expo-spacing-xs, 4px);
  background-color: var(--expo-secondary, #1a1a1a);
  border: 1px solid var(--expo-border-secondary, #4a4a4a);
  border-radius: var(--expo-border-radius-medium, 12px);
  padding: var(--expo-spacing-xs, 4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Position reaction picker based on message ownership */
.custom-message-container--other .reaction-picker-dropdown {
  right: 0;
}

.custom-message-container--own .reaction-picker-dropdown {
  left: 0;
}

/* Reaction picker items */
.reaction-picker-item {
  background: none;
  border: none;
  font-size: 16px;
  padding: var(--expo-spacing-xs, 4px);
  border-radius: var(--expo-border-radius-small, 8px);
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reaction-picker-item:hover {
  background-color: var(--expo-tertiary, #4a4a4a);
  transform: scale(1.2);
}

/* Custom Message Input with Quoted Preview */
.custom-message-input-wrapper {
  display: flex;
  flex-direction: column;
}

.quoted-message-input-preview {
  border-radius: var(--expo-border-radius-medium, 12px);
  margin: var(--expo-spacing-sm, 8px);
  padding: var(--expo-spacing-sm, 8px);
}

.quoted-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--expo-spacing-xs, 4px);
}

.quoted-preview-label {
  font-size: var(--expo-font-size-xs, 12px);
  font-weight: var(--expo-font-weight-bold, 700);
  color: var(--expo-accent, #005fff);
}

.quoted-preview-close {
  background: none;
  border: none;
  color: var(--expo-text-secondary, #72767e);
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.quoted-preview-close:hover {
  background-color: var(--expo-secondary, #1a1a1a);
  color: var(--expo-text, white);
}

.quoted-preview-content {
  font-size: var(--expo-font-size-sm, 14px);
  color: var(--expo-text-secondary, #72767e);
  line-height: 1.4;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-message-replies .str-chat__message-replies-count-button {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  color: var(--expo-accent, #005fff) !important;

  font-weight: var(--expo-font-weight-medium, 500) !important;
}

/* Reactions Host - positioned at bottom like WhatsApp */
.custom-message-reactions-host {
  background: transparent !important;
}

/* Override default reaction list styling */
.custom-message-reactions-host .str-chat__message-reactions-list {
  background: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border: 1px solid red !important;
  margin-top: 0 !important;
}

.str-chat__message-reactions-container .str-chat__message-reactions {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  width: 100% !important;
  flex-direction: row-reverse !important;
  transform: translateY(-30%) !important;
}

/* Individual Reaction Styling */
.custom-message-reactions-host .str-chat__message-reaction {
  background-color: var(--expo-tertiary, #4a4a4a) !important;
  border: 1px solid var(--expo-border-secondary, #4a4a4a) !important;
  border-radius: var(--expo-border-radius-medium, 12px) !important;
  padding: 2px !important;
  font-size: 8px !important; /* Even smaller emoji size */
  line-height: 1 !important; /* Tighten line height */

  /* Override Stream Chat's primary emoji size variable */
  --str-chat__stream-emoji-size: 12px !important;

  /* Override ALL other possible Stream Chat emoji size variables */
  --str-chat__sprite-image-height: 12px !important;
  --str-chat__sprite-image-width: 12px !important;
  --str-chat__emoji-font-size: 12px !important;
  --str-chat__emoji-size: 12px !important;
  --str-chat__emoji-height: 12px !important;
  --str-chat__emoji-width: 12px !important;

  /* Force height/width directly on any child elements */
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
}

/* Target emoji images/spans directly */
.custom-message-reactions-host .str-chat__message-reaction img,
.custom-message-reactions-host .str-chat__message-reaction span,
.custom-message-reactions-host .str-chat__message-reaction .emoji,
.custom-message-reactions-host .str-chat__message-reaction .str-chat__emoji {
  height: 12px !important;
  width: 12px !important;
  font-size: 12px !important;
  line-height: 12px !important;
  max-height: 12px !important;
  max-width: 12px !important;
}

/* Own reactions */
.custom-message-reactions-host .str-chat__message-reaction--own {
  background-color: var(--expo-accent, #005fff) !important;
  border-color: var(--expo-accent, #005fff) !important;
  color: var(--expo-text, white) !important;
}

/* Reaction hover states */
.custom-message-reactions-host .str-chat__message-reaction:hover {
  background-color: var(--expo-tertiary, #4a4a4a) !important;
  opacity: 0.8;
}

.custom-message-reactions-host .str-chat__message-reaction--own:hover {
  background-color: var(--expo-accent, #005fff) !important;
  opacity: 0.8;
}

/* Message Actions (hidden by default, shown on hover) */
.custom-message-bubble .str-chat__message-actions {
  /* You can style the message actions menu here */
}

/* Spacing adjustments for grouped messages */
.custom-message-container.str-chat__li--middle {
  margin-bottom: var(--expo-spacing-xs, 4px);
}

.custom-message-container.str-chat__li--bottom {
  margin-bottom: var(--expo-spacing-lg, 16px);
}
