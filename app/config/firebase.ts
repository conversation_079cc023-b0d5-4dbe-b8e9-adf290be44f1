import { initializeApp, type FirebaseOptions } from "firebase/app";
import { getMessaging, onMessage, type Messaging } from "firebase/messaging";

const firebaseConfig: FirebaseOptions = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
};

const app = initializeApp(firebaseConfig);

// Only initialize messaging in the browser
let messaging: Messaging | null = null;
if (typeof window !== "undefined") {
  try {
    messaging = getMessaging(app);
    console.log("Firebase messaging initialized");
  } catch (error) {
    console.warn("Firebase messaging initialization failed:", error);
    // This might happen if service worker is not available
    messaging = null;
  }
}

export { app, messaging };
