// TODO: check if this is prod or dev, dev use proxy, prod use direct
const isUsingProxy = process.env.NODE_ENV === "development" ? true : false;
const proxyUrl = "http://localhost:5173";

export const HOSTS_CONFIG = {
  isUsingProxy,
  auth: isUsingProxy ? proxyUrl : "https://api.slosphere.com",
  api: isUsingProxy ? `${proxyUrl}/api` : "https://api.slosphere.com",
  web: isUsingProxy ? "http://localhost:5173" : "https://www.slosphere.com",
};

// : "https://sphere-api-backend-268628417819.asia-southeast1.run.app",
// : "https://sphere-auth-backend-268628417819.asia-southeast1.run.app",
