name: Deploy to Google Cloud Storage

on:
  push:
    branches:
      - stage
      # - master  # Uncomment when ready for production deployment

env:
  NODE_VERSION: "20"
  PROJECT_ID: slo-studio-424716
  WORKLOAD_IDENTITY_PROVIDER: "projects/************/locations/global/workloadIdentityPools/github-actions-pool/providers/github"
  SERVICE_ACCOUNT: "<EMAIL>"

jobs:
  deploy-staging:
    if: github.ref == 'refs/heads/stage'
    runs-on: ubuntu-latest
    environment: staging
    permissions:
      contents: "read"
      id-token: "write"

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Build project
        env:
          VITE_STREAM_API_KEY: ${{ secrets.VITE_STREAM_API_KEY }}
        run: bun run build

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account: ${{ env.SERVICE_ACCOUNT }}
          workload_identity_provider: ${{ env.WORKLOAD_IDENTITY_PROVIDER }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ env.PROJECT_ID }}

      - name: Clear existing files in bucket
        run: |
          gsutil -m rm -r gs://sphere-web-staging/** || true

      - name: Upload to Cloud Storage (Staging)
        uses: google-github-actions/upload-cloud-storage@v2
        with:
          path: "build/client"
          destination: "sphere-web-staging"
          glob: "**/*"
          parent: false
          gzip: true
          process_gcloudignore: false

      - name: Set proper MIME types and cache control
        run: |
          # Set long cache for assets (JS, CSS, images) with proper MIME types
          gsutil -m setmeta -h "Cache-Control:public, max-age=********" -h "Content-Type:application/javascript" gs://sphere-web-staging/assets/*.js
          gsutil -m setmeta -h "Cache-Control:public, max-age=********" -h "Content-Type:text/css" gs://sphere-web-staging/assets/*.css
          # Set cache for different asset types (ignore if files don't exist)
          gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-staging/assets/*.png || true
          gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-staging/assets/*.jpg || true
          gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-staging/assets/*.jpeg || true
          gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-staging/assets/*.gif || true
          gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-staging/assets/*.webp || true
          gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-staging/assets/*.svg || true
          gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-staging/assets/*.ico || true
          gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-staging/assets/*.woff || true
          gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-staging/assets/*.woff2 || true
          gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-staging/assets/*.ttf || true
          gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-staging/assets/*.eot || true
          # Set short cache for HTML files
          gsutil -m setmeta -h "Cache-Control:public, max-age=3600" -h "Content-Type:text/html" gs://sphere-web-staging/*.html

      - name: Configure bucket for website hosting
        run: |
          gsutil web set -m index.html -e index.html gs://sphere-web-staging
          gsutil iam ch allUsers:objectViewer gs://sphere-web-staging

      - name: Invalidate CDN cache
        run: |
          gcloud compute url-maps invalidate-cdn-cache sphere-web-staging-lb --path "/*"

  # Production deployment (commented out for now)
  # deploy-production:
  #   if: github.ref == 'refs/heads/master'
  #   runs-on: ubuntu-latest
  #   environment: production
  #   permissions:
  #     contents: 'read'
  #     id-token: 'write'
  #
  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v4
  #
  #   - name: Setup Bun
  #     uses: oven-sh/setup-bun@v1
  #     with:
  #       bun-version: latest
  #
  #   - name: Install dependencies
  #     run: bun install
  #
  #   - name: Build project
  #     env:
  #       VITE_STREAM_API_KEY: ${{ secrets.VITE_STREAM_API_KEY_PROD }}
  #     run: bun run build
  #
  #   - name: Authenticate to Google Cloud
  #     uses: google-github-actions/auth@v2
  #     with:
  #       project_id: ${{ env.PROJECT_ID }}
  #       service_account: ${{ env.SERVICE_ACCOUNT }}
  #       workload_identity_provider: ${{ env.WORKLOAD_IDENTITY_PROVIDER }}
  #
  #   - name: Set up Cloud SDK
  #     uses: google-github-actions/setup-gcloud@v2
  #     with:
  #       project_id: ${{ env.PROJECT_ID }}
  #
  #   - name: Clear existing files in bucket
  #     run: |
  #       gsutil -m rm -r gs://sphere-web-production/** || true
  #
  #   - name: Upload to Cloud Storage (Production)
  #     uses: google-github-actions/upload-cloud-storage@v2
  #     with:
  #       path: 'build/client'
  #       destination: 'sphere-web-production'
  #       glob: '**/*'
  #       parent: false
  #       gzip: true
  #       process_gcloudignore: false
  #
  #   - name: Set proper MIME types and cache control
  #     run: |
  #       # Set long cache for assets (JS, CSS, images) with proper MIME types
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=********" -h "Content-Type:application/javascript" gs://sphere-web-production/assets/*.js
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=********" -h "Content-Type:text/css" gs://sphere-web-production/assets/*.css
  #       # Set cache for different asset types (ignore if files don't exist)
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-production/assets/*.png || true
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-production/assets/*.jpg || true
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-production/assets/*.jpeg || true
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-production/assets/*.gif || true
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-production/assets/*.webp || true
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-production/assets/*.svg || true
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-production/assets/*.ico || true
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-production/assets/*.woff || true
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-production/assets/*.woff2 || true
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-production/assets/*.ttf || true
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://sphere-web-production/assets/*.eot || true
  #       # Set short cache for HTML files
  #       gsutil -m setmeta -h "Cache-Control:public, max-age=3600" -h "Content-Type:text/html" gs://sphere-web-production/*.html
  #
  #   - name: Configure bucket for website hosting
  #     run: |
  #       gsutil web set -m index.html -e index.html gs://sphere-web-production
  #       gsutil iam ch allUsers:objectViewer gs://sphere-web-production
  #
  #   - name: Invalidate CDN cache
  #     run: |
  #       gcloud compute url-maps invalidate-cdn-cache sphere-web-production-lb --path "/*"
