# AGENT.md

## Commands

- `bun run dev` - Start dev server with HMR on http://localhost:5173
- `bun run build` - Create production build
- `bun run typecheck` - Run TypeScript type checking
- `bun run start` - Run production server
- `bunx prettier . --write` - Format code (required before commits)
- No test commands - testing framework not yet configured

## Architecture

- **Tech Stack**: React Router v7, React 19, TypeScript, TailwindCSS v4, Vite, React Query
- **Authentication**: SuperTokens with Email/Password
- **Package Manager**: Bun
- **Path Alias**: `~/` maps to `app/`
- **SSR**: Disabled (SPA mode)

## Code Style

- **API Types**: Always use types from `app/lib/api/types.ts` - never create duplicates
- **Server API**: Use `app/lib/api/loader-queries.ts` for React Router loaders
- **Client API**: Use `app/lib/api/client-queries.ts` for React Query hooks
- **Imports**: Use `~/ `prefix for internal imports
- **Formatting**: Strict TypeScript, ES2022 target, use existing patterns from components
- **No Comments**: Don't add code comments unless explicitly requested

## Key Files

- `app/lib/api/types.ts` - All API TypeScript interfaces
- `app/lib/providers/app-context.tsx` - Global app state management
- `app/routes.ts` - Route definitions (must register new routes here)
