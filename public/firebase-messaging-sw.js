importScripts(
  "https://www.gstatic.com/firebasejs/11.10.0/firebase-app-compat.js"
);
importScripts(
  "https://www.gstatic.com/firebasejs/11.10.0/firebase-messaging-compat.js"
);

// Use same config as main app
firebase.initializeApp({
  apiKey: "AIzaSyBCcXN22OaRGbzFRQq_-OubzJNemaIk99Q",
  authDomain: "slo-studio-424716.firebaseapp.com",
  projectId: "slo-studio-424716",
  storageBucket: "slo-studio-424716.firebasestorage.app",
  messagingSenderId: "268628417819",
  appId: "1:268628417819:web:f439004a64d0f68f1dc7be",
});

const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage(function (payload) {
  console.log("🔔 Background message received:", payload);

  const notificationTitle = payload.notification?.title || "New Notification";
  const notificationOptions = {
    body: payload.notification?.body || "",
    icon: "/favicon.ico",
    badge: "/favicon.ico",
    data: payload.data,
    requireInteraction: false,
    silent: false,
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

self.addEventListener("notificationclick", function (event) {
  console.log("🔔 Notification click received:", event.notification);
  const data = event.notification?.data || {};
  event.notification.close();

  // Mark notification as read if an ID is present
  if (data?.id) {
    // Fire-and-forget – we don't await the promise here
    try {
      fetch(`/api/notifications/${data.id}/read`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
      });
    } catch (err) {
      console.error("Failed to mark notification as read:", err);
    }
  }

  // Determine the URL to navigate to
  let url = "/";
  if (data?.link && typeof data.link === "string") {
    url = data.link; // Use absolute or relative link as-is
  } else if (data?.sender === "stream.chat" && data?.channel_id) {
    url = "/(app)/(tabs)/chats"; // open chats tab first
    // We'll open specific channel below if possible
  } else if (data?.cid) {
    url = `/chat/${data.cid}`;
  }

  // Focus or open the relevant window
  event.waitUntil(
    self.clients
      .matchAll({ type: "window", includeUncontrolled: true })
      .then((clientList) => {
        for (const client of clientList) {
          if (client.url.includes(url) && "focus" in client) {
            return client.focus();
          }
        }
        if (self.clients.openWindow) {
          return self.clients.openWindow(url);
        }
      })
  );
});
