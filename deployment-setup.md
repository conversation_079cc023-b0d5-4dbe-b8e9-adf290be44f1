# Deployment Setup Guide

This guide will help you set up the Google Cloud Storage deployment for the Sphere Web application.

## 1. Google Cloud Setup

### Create the GCS Bucket

```bash
# Create the staging bucket
gsutil mb -p slo-studio-424716 -c standard -l asia-southeast1 gs://sphere-web-staging

# Configure for website hosting
gsutil web set -m index.html -e index.html gs://sphere-web-staging

# Make bucket publicly readable
gsutil iam ch allUsers:objectViewer gs://sphere-web-staging
```

### Create Service Account

```bash
# Create service account for GitHub Actions
gcloud iam service-accounts create github-actions-sphere-web \
    --description="Service account for GitHub Actions deployment" \
    --display-name="GitHub Actions Sphere Web"

# Grant necessary permissions
gcloud projects add-iam-policy-binding slo-studio-424716 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/storage.admin"

gcloud projects add-iam-policy-binding slo-studio-424716 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/compute.networkAdmin"

# Grant CDN cache invalidation permission (required for deployment)
gcloud projects add-iam-policy-binding slo-studio-424716 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/compute.loadBalancerAdmin"

# Generate and download the key
gcloud iam service-accounts keys create github-actions-key.json \
    --iam-account=<EMAIL>
```

## 2. GitHub Repository Setup

### ✅ Authentication (Workload Identity - COMPLETED)

The repository is now configured with **Workload Identity** for secure, keyless authentication to Google Cloud. No service account keys needed!

### Required Secrets

Go to your GitHub repository settings → Secrets and variables → Actions, and add this secret:

1. **`VITE_STREAM_API_KEY`**:
   - Value: `k86g3k78hetj`

~~**`GCP_SA_KEY`** (No longer needed - using Workload Identity)~~

### Environment Protection

1. Go to Settings → Environments
2. Create environment named: `staging`
3. Add protection rules if needed (e.g., require reviews)

## 3. Custom Domain Setup (Optional)

### For Subdomain (e.g., staging.slosphere.com)

```bash
# Run the custom domain setup script
./setup-custom-domain.sh

# Then add DNS A record:
# Name: staging
# Type: A
# Value: *************
```

### For Root Domain (e.g., slosphere.com)

```bash
# Run the root domain setup script
./setup-root-domain.sh

# Then add DNS records:
# A record: @ -> *************
# CNAME record: www -> slosphere.com
```

### SSL Certificate Status

Check certificate provisioning (takes 10-60 minutes):

```bash
# Current certificate (www-first strategy)
gcloud compute ssl-certificates describe sphere-web-www-final --format="yaml(managed)"
```

**www-First Strategy**: Due to persistent SSL certificate issues with root domain validation, we've implemented a www-first approach:

- **Primary Domain**: `https://www.slosphere.com` (HTTPS with SSL certificate)
- **Root Domain**: `http://slosphere.com` (HTTP with redirect to www)
- **SSL Certificate**: Only covers `www.slosphere.com` (more reliable)

**If you need to recreate the www certificate**:

```bash
# Create new www-only certificate
gcloud compute ssl-certificates create sphere-web-www-new \
    --domains=www.slosphere.com --global

# Update HTTPS proxy to use new certificate
gcloud compute target-https-proxies update sphere-web-staging-https-proxy \
    --ssl-certificates=sphere-web-www-new

# Delete old certificate after updating proxy
gcloud compute ssl-certificates delete sphere-web-www-final --quiet
```

### Cloud CDN Setup (Optional for better performance)

```bash
# Create HTTP load balancer with Cloud CDN
# This step is optional but recommended for production-like performance

# 1. Create backend bucket
gcloud compute backend-buckets create sphere-web-staging-backend \
    --gcs-bucket-name=sphere-web-staging

# 2. Create URL map
gcloud compute url-maps create sphere-web-staging-lb \
    --default-backend-bucket=sphere-web-staging-backend

# 3. Create HTTP proxy
gcloud compute target-http-proxies create sphere-web-staging-proxy \
    --url-map=sphere-web-staging-lb

# 4. Create forwarding rule
gcloud compute forwarding-rules create sphere-web-staging-rule \
    --global \
    --target-http-proxy=sphere-web-staging-proxy \
    --ports=80
```

## 4. Testing the Deployment

1. Push code to the `stage` branch:

   ```bash
   git checkout stage
   git push origin stage
   ```

2. Check GitHub Actions tab for deployment progress

3. Access your site at:
   - Load balancer URL: `http://*************`
   - Direct bucket URL: `https://storage.googleapis.com/sphere-web-staging/index.html`
   - Custom domain (if configured): `https://slosphere.com`

## 5. Production Setup (Future)

### Important Infrastructure Information

**Current Setup (Staging):**

- **GCS Bucket**: `sphere-web-staging`
- **Load Balancer**: `sphere-web-staging-lb`
- **External IP**: `*************`
- **SSL Certificate**: `sphere-web-www-final` (www-first strategy implementation)
- **Primary Domain**: `www.slosphere.com` (HTTPS - main site access)
- **Secondary Domain**: `slosphere.com` (HTTP - redirects to www)

**CRITICAL**: Using www-first strategy due to root domain SSL certificate issues. DNS configuration:

- **www.slosphere.com** → `***********` (HTTPS load balancer)
- **slosphere.com** → `*************` (HTTP load balancer for redirects)

**Domain Configuration:**

- **Root Domain** (`slosphere.com`): Static site via Load Balancer/CDN
- **Admin Domain** (`admin.slosphere.com`): SSR app via App Engine
- **API Domain** (`api.slosphere.com`): Backend via App Engine

### For Future Production Deployment:

#### 1. Create Production Infrastructure

```bash
# Create production bucket
gsutil mb -p slo-studio-424716 -c standard -l asia-southeast1 gs://sphere-web-production

# Configure for website hosting
gsutil web set -m index.html -e index.html gs://sphere-web-production
gsutil iam ch allUsers:objectViewer gs://sphere-web-production

# Create production backend bucket
gcloud compute backend-buckets create sphere-web-production-backend \
    --gcs-bucket-name=sphere-web-production

# Enable CDN
gcloud compute backend-buckets update sphere-web-production-backend --enable-cdn

# Create production URL map
gcloud compute url-maps create sphere-web-production-lb \
    --default-backend-bucket=sphere-web-production-backend

# Create production SSL certificate (for separate production domain)
gcloud compute ssl-certificates create sphere-web-production-ssl \
    --domains=prod.slosphere.com,www.prod.slosphere.com \
    --global

# Create HTTPS proxy
gcloud compute target-https-proxies create sphere-web-production-https-proxy \
    --url-map=sphere-web-production-lb \
    --ssl-certificates=sphere-web-production-ssl

# Create HTTP proxy (for redirect)
gcloud compute target-http-proxies create sphere-web-production-http-proxy \
    --url-map=sphere-web-production-lb

# Create forwarding rules
gcloud compute forwarding-rules create sphere-web-production-https-rule \
    --global \
    --target-https-proxy=sphere-web-production-https-proxy \
    --ports=443

gcloud compute forwarding-rules create sphere-web-production-http-rule \
    --global \
    --target-http-proxy=sphere-web-production-http-proxy \
    --ports=80
```

#### 2. GitHub Actions Updates

1. Uncomment the master branch deployment in `.github/workflows/deploy.yml`
2. Update production job to use production infrastructure:
   - Change bucket to `sphere-web-production`
   - Update CDN invalidation to `sphere-web-production-lb`

#### 3. Environment Variables

Add production secrets to GitHub:

- `VITE_STREAM_API_KEY_PROD`

#### 4. Domain Strategy Options

**Option A: Separate Production Domain**

- Use `prod.slosphere.com` for production
- Keep `slosphere.com` for staging
- Requires separate SSL certificate and DNS setup

**Option B: Move Root Domain to Production**

- Move `slosphere.com` to production infrastructure
- Use `staging.slosphere.com` for staging
- Update DNS and SSL certificates accordingly

#### 5. DNS Configuration (Option B - Root Domain Production)

When moving to production on root domain:

```bash
# Update A record
slosphere.com. A 300 [NEW_PRODUCTION_IP]

# Keep existing subdomains
admin.slosphere.com. CNAME 300 ghs.googlehosted.com.
api.slosphere.com. CNAME 300 ghs.googlehosted.com.
```

### Current App Engine Services

- **Admin**: `admin.slosphere.com` (SSR application)
- **API**: `api.slosphere.com` (Backend services)
- **Keep these unchanged** during production migration

## Important Notes

- The workflow automatically gzips files for better performance
- Static assets (JS/CSS) are cached for 1 year
- HTML files are cached for 1 hour
- The bucket is cleared before each deployment to ensure clean state

## Troubleshooting

### Critical Infrastructure Issues

#### 1. SSL Certificate Stuck in FAILED_NOT_VISIBLE

**Problem**: SSL certificate shows `FAILED_NOT_VISIBLE` and won't provision.

**Root Cause**: DNS pointing to wrong load balancer IP or certificate got stuck in provisioning.

**Solution**:

```bash
# Check current DNS (should point to HTTPS load balancer)
nslookup slosphere.com *******
nslookup www.slosphere.com *******

# Get correct IPs
gcloud compute forwarding-rules list --filter="name~sphere-web-staging"
# HTTP:  sphere-web-staging-rule      *************
# HTTPS: sphere-web-staging-https-rule ***********

# DNS should point to HTTPS IP: ***********
```

If DNS is correct but certificate still stuck, consider the www-first strategy (see SSL Certificate Status section above).

#### 1b. www-First Strategy Solution

**Problem**: Root domain SSL certificates persistently fail with `FAILED_NOT_VISIBLE` despite correct DNS.

**Solution**: Implement www-first strategy to avoid root domain SSL complexities.

**Implementation**:

```bash
# Create www-only certificate
gcloud compute ssl-certificates create sphere-web-www-final \
    --domains=www.slosphere.com --global

# Update HTTPS proxy
gcloud compute target-https-proxies update sphere-web-staging-https-proxy \
    --ssl-certificates=sphere-web-www-final

# Update DNS records
# www.slosphere.com   A    ***********   (HTTPS load balancer)
# slosphere.com       A    ************* (HTTP load balancer for redirects)
```

**Benefits**:

- More reliable SSL certificate provisioning
- Standard industry practice
- Better CDN and DNS flexibility
- Cleaner canonical URL structure

**Application Configuration**: Update `app/config/hosts.ts` to use www domain:

```typescript
export const HOSTS_CONFIG = {
  isUsingProxy,
  auth: isUsingProxy ? proxyUrl : "https://api.slosphere.com",
  api: isUsingProxy ? `${proxyUrl}/api` : "https://api.slosphere.com",
  web: isUsingProxy ? "http://localhost:5173" : "https://www.slosphere.com",
};
```

#### 2. MIME Type Issues (CSS/JS served as text/html)

**Problem**: Browser console shows "Expected JavaScript/CSS but got text/html".

**Root Cause**: Deployment was setting all files to `content-type: text/html`.

**Solution**: Fixed in `.github/workflows/deploy.yml` - proper MIME types now set:

- JavaScript: `application/javascript`
- CSS: `text/css`
- HTML: `text/html`

#### 3. 404 Error on Custom Domain

**Problem**: Domain shows Google 404 page instead of website.

**Root Cause**: DNS pointing to wrong load balancer IP.

**Solution**:

```bash
# Check which IP your domain points to
nslookup slosphere.com

# Should point to HTTPS load balancer IP
# If pointing to HTTP IP (*************), update DNS to HTTPS IP (***********)
```

### Current Infrastructure Status

**Load Balancer IPs**:

- **HTTP**: `*************` (sphere-web-staging-rule)
- **HTTPS**: `***********` (sphere-web-staging-https-rule)

**SSL Certificate**: `sphere-web-www-final` (www-first strategy - active certificate)

**DNS Configuration (www-First Strategy)**:

```
www.slosphere.com   A    ***********   (HTTPS load balancer - PRIMARY)
slosphere.com       A    ************* (HTTP load balancer - redirect)
```

**Bucket Configuration**:

- **Staging**: `gs://sphere-web-staging`
- **Backend**: `sphere-web-staging-backend`
- **CDN**: Enabled with proper cache policies

### Permission Issues

#### CDN Cache Invalidation Permission Error

**Problem**: GitHub Actions fails with "Required 'compute.urlMaps.invalidateCache' permission" error.

**Solution**: Grant Load Balancer Admin role to service account:

```bash
gcloud projects add-iam-policy-binding slo-studio-424716 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/compute.loadBalancerAdmin"
```

### Common Issues

1. **Permission Denied**: Check service account permissions (see Permission Issues section)
2. **Bucket Not Found**: Ensure bucket exists and name is correct
3. **Build Fails**: Check environment variables are set correctly
4. **Root Domain HTTPS Issues**: Use www-first strategy (see SSL Certificate section)
5. **CSS/JS Not Loading**: Check for MIME type errors in browser console
6. **CDN Invalidation Fails**: Service account needs `compute.loadBalancerAdmin` role
7. **SSL Certificate Stuck**: Consider www-only certificate approach
8. **DNS Configuration**: Use separate IPs for HTTP and HTTPS with www-first strategy

### Monitoring Commands

```bash
# Check SSL certificate status (www-first strategy)
gcloud compute ssl-certificates describe sphere-web-www-final --format="value(managed.status)"

# Check DNS resolution for www-first strategy
nslookup www.slosphere.com *******  # Should point to *********** (HTTPS)
nslookup slosphere.com *******      # Should point to ************* (HTTP)

# Test HTTPS access (primary domain)
curl -I https://www.slosphere.com

# Test HTTP access (root domain - should redirect)
curl -I http://slosphere.com

# Test HTTPS access to root domain (should work once redirects are set up)
curl -I https://slosphere.com

# Invalidate CDN cache
gcloud compute url-maps invalidate-cdn-cache sphere-web-staging-lb --path "/*"
```

### Logs

Check GitHub Actions logs for detailed error messages:

- Go to Actions tab in GitHub
- Click on the failed workflow
- Expand the failed step to see error details
