# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Important Development Guidelines

### API Types

**ALWAYS use types from `app/lib/api/types.ts`** when working with API data. This file contains all TypeScript interfaces and types for API responses, including:

- `Module` types (CourseModule, FeedModule, EventsModule, etc.)
- `Course`, `Section`, `Lesson` types for course data
- `Group`, `Cohort` types for organizational structure
- `Notification`, `NotificationsResponse` types for notifications
- `LiveClass`, `LiveClassResponse`, `LiveClassesResponse` types for live classes
- `ProfileByIdResponse`, `ModuleResponse` types
- Response types like `APIResponse<T>`, `CourseResponse`, etc.

Never create duplicate type definitions - import and use the existing types from this file.

### API Calls Pattern

The codebase follows a clear separation between server-side and client-side API calls:

#### Server-Side API Calls (SSR/Loaders)

**Use `app/lib/api/loader-queries.ts`** for all server-side API calls in React Router loaders:

- Contains all fetch functions needed by route loaders
- Handles authentication via cookie headers automatically
- Provides consistent error handling with `HttpError`
- Examples: `fetchUserGroups()`, `fetchCohort()`, `fetchModuleWithData()`

#### Client-Side API Calls (React Query)

**Use `app/lib/api/client-queries.ts`** for all client-side API calls:

- Contains React Query hooks for data fetching and mutations
- Handles caching, optimistic updates, and invalidation
- Examples: `useProfile()`, `useGroups()`, `useCompleteLesson()`

Never make direct fetch() calls in loaders - always use the functions from loader-queries.ts.

## Commands

### Development

- `bun run dev` - Start development server with HMR on http://localhost:5173
- `bun run build` - Create production build
- `bun run start` - Run production server
- `bun run typecheck` - Run TypeScript type checking

### Package Management

This project uses **Bun** as the package manager:

- `bun install` - Install dependencies
- `bun add <package>` - Add new dependency
- `bun add -d <package>` - Add development dependency

## Architecture

### Tech Stack

- **React Router v7** - Full-stack React framework
- **React 19.1.0** - UI framework
- **TypeScript** - Type safety
- **TailwindCSS v4** - Styling (uses new @theme syntax)
- **Vite** - Build tool
- **React Query (TanStack Query)** - Data fetching and caching
- **SuperTokens** - Authentication
- **Stream Chat** - Real-time messaging functionality
- **HLS.js** - Video playback for HLS streams
- **Lucide React** - Icon library

### Project Structure

- **`app/routes/`** - File-based routing (defined in `app/routes.ts`)
- **`app/lib/api/`** - API client layer with custom error handling
  - `types.ts` - TypeScript interfaces for all API responses
  - `loader-queries.ts` - Server-side API calls for React Router loaders
  - `client-queries.ts` - Client-side React Query hooks
- **`app/lib/providers/`** - React context providers
  - `app-context.tsx` - Global app state (auth, chat client, current group/cohort)
  - `query-client.tsx` - React Query configuration
- **`app/lib/auth/`** - Authentication utilities
  - `session.tsx` - Session management helpers
- **`app/config/`** - Configuration files (auth, hosts)
- **`app/components/`** - Reusable UI components
  - `AppLayout.tsx` - Main app layout wrapper for authenticated routes
  - `Sidebar.tsx` - Main navigation sidebar with groups
  - `GroupLayout.tsx` - Layout wrapper for group pages with cohort navigation
  - `CohortSelectionModal.tsx` - Modal for cohort selection
  - `CourseTOC.tsx` - Course table of contents component
  - `VideoPlayerHLS.tsx` - HLS video player component
  - `modules/` - Module-specific components (CourseModule, EventsModule, etc.)
- **`app/lib/stream/`** - Stream Chat utilities
- **`app/styles/`** - Additional stylesheets
  - `chat-layout.css` - Stream Chat layout styles
- **`~/*`** - Path alias for `app/*` directory

### Key Configuration

- **Vite Proxy**: Development API calls proxy to production backends
  - `/auth-api/*` → `https://api.slosphere.com`
  - `/api/*` → `https://api.slosphere.com`
- **SSR**: Server-side rendering is disabled (`ssr: false` in `react-router.config.ts`)
- **TypeScript**: Strict mode enabled with ES2022 target
- **Build Output**: Builds output to `build/client` and `build/server` directories

### Authentication

Uses SuperTokens with Email/Password recipe. Configuration in `app/config/auth.ts`.

### Key Features

- **Dual Sidebar Navigation**:
  - Primary sidebar shows user groups and system navigation
  - Secondary sidebar (in GroupLayout) displays cohorts and their modules
- **Dynamic Module System**: Cohorts can have different module types (course, events, discussion, links)
- **Real-time Data Fetching**: Uses React Query for efficient data fetching and caching

### API Integration

- Groups and cohorts are fetched from the API with authentication cookies
- Module types supported: `course`, `events`, `discussion`, `links`
- Modules are sorted by their `order` property for consistent display

### Docker Deployment

Multi-stage Dockerfile optimized for production:

- Build stage: Compiles TypeScript and bundles assets using Bun
- Production stage: Only includes production dependencies and built assets
- Exposes port 3000
- Uses Node 20 Alpine for lightweight container

### Docker Commands

- `docker build -t sphere-web .` - Build Docker image
- `docker run -p 3000:3000 sphere-web` - Run container locally

## Additional Guidelines

### State Management

- **AppContext** manages global state including:
  - User authentication state
  - Current group/cohort selection (persisted to localStorage)
  - Stream Chat client instance
  - Provides cleanup utilities for logout

### React Query Configuration

- Stale time: 1 minute
- Refetch on window focus enabled
- Retry: 3 attempts with exponential backoff
- Query client configured in `app/lib/providers/query-client.tsx`

### Error Handling

- All API errors use the custom `HttpError` class
- Consistent error boundaries and fallbacks
- Proper error messages for user feedback

### Environment Variables

- `VITE_STREAM_API_KEY` - Required for Stream Chat functionality
- `NODE_ENV` - Affects proxy behavior in development (uses proxy when not set to 'production')

### Routing Patterns

- Nested layouts with `AppLayout` for authenticated routes
- Dynamic route parameters use brackets: `[groupId]`, `[cohortId]`, `[moduleId]`
- Route definitions centralized in `app/routes.ts`

## CI/CD and Deployment

### GitHub Actions

The project uses GitHub Actions for continuous deployment to Google Cloud Storage:

- **Workflow**: `.github/workflows/deploy-to-gcs.yml`
- **Authentication**: Uses Workload Identity Federation (not service account keys)
- **Staging**: Automatically deploys `stage` branch to staging environment
- **Production**: Production deployment setup available (currently commented out for `master` branch)
- **Steps**:
  1. Checkout code
  2. Setup Bun runtime
  3. Install dependencies with `bun install`
  4. Build project with `bun run build`
  5. Authenticate to Google Cloud using Workload Identity
  6. Upload build artifacts to GCS bucket
  7. Invalidate Cloud CDN cache

### Google Cloud Storage Deployment

- **Detailed Setup**: See `deployment-setup.md` for comprehensive GCS deployment instructions
- **Infrastructure**:
  - Google Cloud Storage bucket for static hosting
  - Cloud Load Balancer for HTTPS
  - Cloud CDN for global content delivery
  - SSL certificates managed by Google

### Deployment Files

- **`.github/workflows/deploy-to-gcs.yml`** - GitHub Actions workflow
- **`deployment-setup.md`** - Detailed deployment setup guide
- **`Dockerfile`** - Container configuration for alternative deployment methods

## Development Workflow Reminders

### Route Registration

- When adding a new path, remember to add to the routes.ts file to register the route. This will also generate type for this route.

## Remember

### Prettier

Remember to always format the code using `bunx prettier . --write`.

### ast-grep

You run in an environment where `ast-grep` is available; whenever a search requires syntax-aware or structural matching, default to `ast-grep --lang rust -p '<pattern>'` (or set `--lang` appropriately) and avoid falling back to text-only tools like `rg` or `grep` unless I explicitly request a plain-text search.

### Use Gemini CLI

Instruct my Claude to use Gemini CLI when it needs to summarize. Why? Because Gemini has more context (but it sucks at tooling).

When analyzing large codebases or multiple files that might exceed context limits, use the Gemini CLI with its massive context window.
Use gemini -p when:

- Analyzing entire codebases or large directories
- Comparing multiple large files
- Need to understand project-wide patterns or architecture
- Checking for the presence of certain coding patterns across the entire codebase

Examples:

```bash
gemini -p "@src/main.py Explain this file's purpose and structure"
gemini -p "@src/ Summarize the architecture of this codebase"
gemini -p "@src/ Are there any React hooks that handle WebSocket connections? List them with file paths"
```

### Using Gemini CLI for Large Codebase Analysis

When analyzing large codebases or multiple files that might exceed context limits, use the Gemini CLI with its massive
context window. Use `gemini -p` to leverage Google Gemini's large context capacity.

#### File and Directory Inclusion Syntax

Use the `@` syntax to include files and directories in your Gemini prompts. The paths should be relative to WHERE you run the
gemini command:

###### Examples:

**Single file analysis:**
gemini -p "@src/main.py Explain this file's purpose and structure"

Multiple files:
gemini -p "@package.json @src/index.js Analyze the dependencies used in the code"

Entire directory:
gemini -p "@src/ Summarize the architecture of this codebase"

Multiple directories:
gemini -p "@src/ @tests/ Analyze test coverage for the source code"

Current directory and subdirectories:
gemini -p "@./ Give me an overview of this entire project"

# Or use --all_files flag:

gemini --all_files -p "Analyze the project structure and dependencies"

Implementation Verification Examples

Check if a feature is implemented:
gemini -p "@src/ @lib/ Has dark mode been implemented in this codebase? Show me the relevant files and functions"

Verify authentication implementation:
gemini -p "@src/ @middleware/ Is JWT authentication implemented? List all auth-related endpoints and middleware"

Check for specific patterns:
gemini -p "@src/ Are there any React hooks that handle WebSocket connections? List them with file paths"

Verify error handling:
gemini -p "@src/ @api/ Is proper error handling implemented for all API endpoints? Show examples of try-catch blocks"

Check for rate limiting:
gemini -p "@backend/ @middleware/ Is rate limiting implemented for the API? Show the implementation details"

Verify caching strategy:
gemini -p "@src/ @lib/ @services/ Is Redis caching implemented? List all cache-related functions and their usage"

Check for specific security measures:
gemini -p "@src/ @api/ Are SQL injection protections implemented? Show how user inputs are sanitized"

Verify test coverage for features:
gemini -p "@src/payment/ @tests/ Is the payment processing module fully tested? List all test cases"

When to Use Gemini CLI

Use gemini -p when:

- Analyzing entire codebases or large directories
- Comparing multiple large files
- Need to understand project-wide patterns or architecture
- Current context window is insufficient for the task
- Working with files totaling more than 100KB
- Verifying if specific features, patterns, or security measures are implemented
- Checking for the presence of certain coding patterns across the entire codebase

Important Notes

- Paths in @ syntax are relative to your current working directory when invoking gemini
- The CLI will include file contents directly in the context
- No need for --yolo flag for read-only analysis
- Gemini's context window can handle entire codebases that would overflow Claude's context
- When checking implementations, be specific about what you're looking for to get accurate results
