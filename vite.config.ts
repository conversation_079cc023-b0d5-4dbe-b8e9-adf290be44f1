import { reactRouter } from "@react-router/dev/vite";
import tailwindcss from "@tailwindcss/vite";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig({
  plugins: [tailwindcss(), reactRouter(), tsconfigPaths()],
  define: {
    global: "globalThis",
    "process.env": {},
  },
  server: {
    proxy: {
      "/auth-api": {
        target: "https://api.slosphere.com",
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/auth-api/, "/auth"),
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.error("[proxy] proxy error", err);
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log(
              "[proxy] Sending Request to the Target:",
              req.method,
              req.url
            );
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log(
              "[proxy] Received Response from the Target:",
              proxyRes.statusCode,
              req.url
            );
          });
        },
      },
      "/api": {
        target: "https://api.slosphere.com",
        changeOrigin: true,
        secure: false,
        ws: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.error("[proxy] Proxy error", err);
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log(
              "[proxy] Sending Request to the Target:",
              req.method,
              req.url
            );
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log(
              "[proxy] Received Response from the Target:",
              proxyRes.statusCode,
              req.url
            );
          });
        },
      },
    },
  },
});
